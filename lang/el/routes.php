<?php

return [
    'login'           => [
        'furl'  => "/",
        'title' => "Είσοδος Χρήστη"
    ],
    'dashboard'       => [
        'furl'  => "/dashboard",
        'title' => "Dashboard"
    ],
    'tagIndex'        => [
        'furl'  => "/tags",
        'title' => "Tags"
    ],
    'tagDelete'       => [
        'furl'  => "/tags/{id}",
        'title' => "REDIRECT"
    ],
    'massDeleteTags'  => [
        'furl'  => "/tag/mass",
        'title' => "REDIRECT"
    ],
    'tagAdd'          => [
        'furl'  => "/tag/add",
        'title' => "Προσθήκη νέου Tag"
    ],
    'tagUpdate'       => [
        'furl'  => "/tag/edit/{id}",
        'title' => "Ενημέρωση Tag"
    ],
    'register'        => [
        'furl'  => "/register",
        'title' => "Εγγραφή Νέου Χρήστη"
    ],
    'logoff'          => [
        'furl'  => "/logoff",
        'title' => "REDIRECT PAGE"
    ],
    'forgotPassword'      => [
        'furl'  => "/forgot-password",
        'title' => "Ξέχασα τον κωδικό μου"
    ],
    'resetPassword'   => [
        'furl'  => "/reset-password/{token}",
        'title' => "Δημιουργία νέου κωδικού"
    ],
    'roomAdd'         => [
        'furl'  => "/room/add",
        'title' => "Προσθήκη νέου δωματίου"
    ],
    'roomIndex'       => [
        'furl'  => "/rooms",
        'title' => "Δωμάτια"
    ],
    'roomDelete'      => [
        'furl'  => "/room/delete/{id}",
        'title' => "REDIRECT"
    ],
    'roomUpdate'      => [
        'furl'  => "/room/edit/{id}",
        'title' => "Ενημέρωση δωματίου"
    ],
    'pendingConfirmation' => [
        'furl'  => "/pending-confirmation",
        'title' => "Αναμονή έγκρισης λογαριασμού"
    ],
    'userIndex'       => [
        'furl'  => "/users",
        'title' => "Χρήστες"
    ],
    'procedureIndex'  => [
        'furl'  => "/procedures",
        'title' => "Διαδικασίες"
    ],
    'procedureDelete' => [
        'furl'  => "/procedures/{id}",
        'title' => "REDIRECT"
    ],
    'procedureAdd'    => [
        'furl'  => "/procedure/add",
        'title' => "Προσθήκη νέας Διαδικασίας"
    ],
    'procedureUpdate' => [
        'furl'  => "/procedure/edit/{id}",
        'title' => "Ενημέρωση Διαδικασίας"
    ],
];
