<?php

return [
    'login'          => [
        'furl'  => "/en",
        'title' => "User Login"
    ],
    'dashboard'      => [
        'furl'  => "/en/dashboard",
        'title' => "Dashboard"
    ],
    'tagIndex'       => [
        'furl'  => "/en/tags",
        'title' => "Tags"
    ],
    'tagDelete'      => [
        'furl'  => "/en/tags/{id}",
        'title' => "REDIRECT"
    ],
    'massDeleteTags' => [
        'furl'  => "/en/tag/mass",
        'title' => "AJAX CALL"
    ],
    'tagAdd'         => [
        'furl'  => "/en/tag/add",
        'title' => "Add new Tag"
    ],
    'tagUpdate'      => [
        'furl'  => "/en/tag/edit/{id}",
        'title' => "Update Tag"
		],
		'register'      => [
        'furl'  => "/register",
        'title' => "Register New User"
    ],
    'logoff'         => [
        'furl'  => "/en/logoff",
        'title' => "REDIRECT PAGE"
    ],
    'forgotPassword' => [
        'furl'  => "/en/forgot-password",
        'title' => "Forgot my password"
    ],
    'resetPassword'  => [
        'furl'  => "/en/reset-password/{token}",
        'title' => "Create new password"
    ],
    'roomAdd'      => [
        'furl'  => "/en/room/add",
        'title' => "Add new room"
    ],
    'roomIndex'       => [
        'furl'  => "/en/rooms",
        'title' => "Rooms"
    ],
    'roomDelete'      => [
        'furl'  => "/en/room/delete/{id}",
        'title' => "REDIRECT"
    ],
    'roomUpdate'      => [
        'furl'  => "/en/room/edit/{id}",
        'title' => "Update room"
    ],
    'pendingConfirmation' => [
        'furl'  => "/en/pending-confirmation",
        'title' => "Awaiting account approval"
    ],
    'procedureIndex'      => [
        'furl'  => "/en/procedures",
        'title' => "Procedures"
    ],
    'procedureDelete'     => [
        'furl'  => "/en//procedures/{id}",
        'title' => "REDIRECT"
    ],
    'procedureAdd'        => [
        'furl'  => "/en//procedure/add",
        'title' => "Add new procedure"
    ],
    'procedureUpdate'     => [
        'furl'  => "/en//procedure/edit/{id}",
        'title' => "Update procedure"
    ],
];
