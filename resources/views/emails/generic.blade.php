<html xmlns:v="urn:schemas-microsoft-com:vml"
      xmlns:o="urn:schemas-microsoft-com:office:office"
      xmlns:w="urn:schemas-microsoft-com:office:word"
      xmlns:m="http://schemas.microsoft.com/office/2004/12/omml"
      xmlns="http://www.w3.org/TR/REC-html40">

<head>
    <meta http-equiv=Content-Type content="text/html; charset=unicode">
    <meta name=ProgId content=Word.Document>
    <meta name=Generator content="Microsoft Word 15">
    <meta name=Originator content="Microsoft Word 15">
    <!--[if !mso]>
    <style>
        v\: * {
            behavior: url(#default#VML);
        }

        o\: * {
            behavior: url(#default#VML);
        }

        w\: * {
            behavior: url(#default#VML);
        }

        .shape {
            behavior: url(#default#VML);
        }
    </style>

    <![endif]-->
    <!--[if gte mso 9]><xml>
     <w:WordDocument>
      <w:Zoom>0</w:Zoom>
      <w:DocumentKind>DocumentEmail</w:DocumentKind>
      <w:TrackMoves/>
      <w:TrackFormatting/>
      <w:ValidateAgainstSchemas/>
      <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
      <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
      <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
      <w:DoNotPromoteQF/>
      <w:LidThemeOther>EN-US</w:LidThemeOther>
      <w:LidThemeAsian>X-NONE</w:LidThemeAsian>
      <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
      <w:Compatibility>
       <w:DoNotExpandShiftReturn/>
       <w:BreakWrappedTables/>
       <w:SnapToGridInCell/>
       <w:WrapTextWithPunct/>
       <w:UseAsianBreakRules/>
       <w:DontGrowAutofit/>
       <w:SplitPgBreakAndParaMark/>
       <w:EnableOpenTypeKerning/>
       <w:DontFlipMirrorIndents/>
       <w:OverrideTableStyleHps/>
      </w:Compatibility>
      <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel>
      <m:mathPr>
       <m:mathFont m:val="Cambria Math"/>
       <m:brkBin m:val="before"/>
       <m:brkBinSub m:val="&#45;-"/>
       <m:smallFrac m:val="off"/>
       <m:dispDef/>
       <m:lMargin m:val="0"/>
       <m:rMargin m:val="0"/>
       <m:defJc m:val="centerGroup"/>
       <m:wrapIndent m:val="1440"/>
       <m:intLim m:val="subSup"/>
       <m:naryLim m:val="undOvr"/>
      </m:mathPr></w:WordDocument>
    </xml><![endif]--><!--[if gte mso 9]><xml>
        <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false"
                        DefSemiHidden="false" DefQFormat="false" DefPriority="99"
                        LatentStyleCount="376">
            <w:LsdException Locked="false" Priority="0" QFormat="true" Name="Normal"/>
            <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 1"/>
            <w:LsdException Locked="false" Priority="9" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="heading 2"/>
            <w:LsdException Locked="false" Priority="9" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="heading 3"/>
            <w:LsdException Locked="false" Priority="9" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="heading 4"/>
            <w:LsdException Locked="false" Priority="9" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="heading 5"/>
            <w:LsdException Locked="false" Priority="9" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="heading 6"/>
            <w:LsdException Locked="false" Priority="9" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="heading 7"/>
            <w:LsdException Locked="false" Priority="9" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="heading 8"/>
            <w:LsdException Locked="false" Priority="9" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="heading 9"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 5"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 6"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 7"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 8"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index 9"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 1"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 2"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 3"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 4"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 5"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 6"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 7"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 8"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" Name="toc 9"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Normal Indent"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="footnote text"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="annotation text"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="header"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="footer"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="index heading"/>
            <w:LsdException Locked="false" Priority="35" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="caption"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="table of figures"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="envelope address"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="envelope return"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="footnote reference"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="annotation reference"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="line number"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="page number"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="endnote reference"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="endnote text"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="table of authorities"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="macro"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="toa heading"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Bullet"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Number"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List 5"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Bullet 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Bullet 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Bullet 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Bullet 5"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Number 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Number 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Number 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Number 5"/>
            <w:LsdException Locked="false" Priority="10" QFormat="true" Name="Title"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Closing"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Signature"/>
            <w:LsdException Locked="false" Priority="1" SemiHidden="true"
                            UnhideWhenUsed="true" Name="Default Paragraph Font"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Body Text"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Body Text Indent"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Continue"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Continue 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Continue 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Continue 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="List Continue 5"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Message Header"/>
            <w:LsdException Locked="false" Priority="11" QFormat="true" Name="Subtitle"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Salutation"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Date"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Body Text First Indent"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Body Text First Indent 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Note Heading"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Body Text 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Body Text 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Body Text Indent 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Body Text Indent 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Block Text"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Hyperlink"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="FollowedHyperlink"/>
            <w:LsdException Locked="false" Priority="22" QFormat="true" Name="Strong"/>
            <w:LsdException Locked="false" Priority="20" QFormat="true" Name="Emphasis"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Document Map"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Plain Text"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="E-mail Signature"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Top of Form"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Bottom of Form"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Normal (Web)"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Acronym"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Address"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Cite"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Code"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Definition"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Keyboard"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Preformatted"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Sample"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Typewriter"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="HTML Variable"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Normal Table"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="annotation subject"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="No List"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Outline List 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Outline List 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Outline List 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Simple 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Simple 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Simple 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Classic 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Classic 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Classic 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Classic 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Colorful 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Colorful 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Colorful 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Columns 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Columns 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Columns 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Columns 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Columns 5"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Grid 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Grid 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Grid 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Grid 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Grid 5"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Grid 6"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Grid 7"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Grid 8"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table List 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table List 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table List 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table List 4"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table List 5"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table List 6"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table List 7"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table List 8"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table 3D effects 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table 3D effects 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table 3D effects 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Contemporary"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Elegant"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Professional"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Subtle 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Subtle 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Web 1"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Web 2"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Web 3"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Balloon Text"/>
            <w:LsdException Locked="false" Priority="39" Name="Table Grid"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Table Theme"/>
            <w:LsdException Locked="false" SemiHidden="true" Name="Placeholder Text"/>
            <w:LsdException Locked="false" Priority="1" QFormat="true" Name="No Spacing"/>
            <w:LsdException Locked="false" Priority="60" Name="Light Shading"/>
            <w:LsdException Locked="false" Priority="61" Name="Light List"/>
            <w:LsdException Locked="false" Priority="62" Name="Light Grid"/>
            <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1"/>
            <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2"/>
            <w:LsdException Locked="false" Priority="65" Name="Medium List 1"/>
            <w:LsdException Locked="false" Priority="66" Name="Medium List 2"/>
            <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1"/>
            <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2"/>
            <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3"/>
            <w:LsdException Locked="false" Priority="70" Name="Dark List"/>
            <w:LsdException Locked="false" Priority="71" Name="Colorful Shading"/>
            <w:LsdException Locked="false" Priority="72" Name="Colorful List"/>
            <w:LsdException Locked="false" Priority="73" Name="Colorful Grid"/>
            <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 1"/>
            <w:LsdException Locked="false" Priority="61" Name="Light List Accent 1"/>
            <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 1"/>
            <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 1"/>
            <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 1"/>
            <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 1"/>
            <w:LsdException Locked="false" SemiHidden="true" Name="Revision"/>
            <w:LsdException Locked="false" Priority="34" QFormat="true"
                            Name="List Paragraph"/>
            <w:LsdException Locked="false" Priority="29" QFormat="true" Name="Quote"/>
            <w:LsdException Locked="false" Priority="30" QFormat="true"
                            Name="Intense Quote"/>
            <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 1"/>
            <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 1"/>
            <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 1"/>
            <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 1"/>
            <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 1"/>
            <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 1"/>
            <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 1"/>
            <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 1"/>
            <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 2"/>
            <w:LsdException Locked="false" Priority="61" Name="Light List Accent 2"/>
            <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 2"/>
            <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 2"/>
            <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 2"/>
            <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 2"/>
            <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 2"/>
            <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 2"/>
            <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 2"/>
            <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 2"/>
            <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 2"/>
            <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 2"/>
            <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 2"/>
            <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 2"/>
            <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 3"/>
            <w:LsdException Locked="false" Priority="61" Name="Light List Accent 3"/>
            <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 3"/>
            <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 3"/>
            <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 3"/>
            <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 3"/>
            <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 3"/>
            <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 3"/>
            <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 3"/>
            <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 3"/>
            <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 3"/>
            <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 3"/>
            <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 3"/>
            <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 3"/>
            <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 4"/>
            <w:LsdException Locked="false" Priority="61" Name="Light List Accent 4"/>
            <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 4"/>
            <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 4"/>
            <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 4"/>
            <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 4"/>
            <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 4"/>
            <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 4"/>
            <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 4"/>
            <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 4"/>
            <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 4"/>
            <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 4"/>
            <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 4"/>
            <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 4"/>
            <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 5"/>
            <w:LsdException Locked="false" Priority="61" Name="Light List Accent 5"/>
            <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 5"/>
            <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 5"/>
            <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 5"/>
            <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 5"/>
            <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 5"/>
            <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 5"/>
            <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 5"/>
            <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 5"/>
            <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 5"/>
            <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 5"/>
            <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 5"/>
            <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 5"/>
            <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 6"/>
            <w:LsdException Locked="false" Priority="61" Name="Light List Accent 6"/>
            <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 6"/>
            <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 6"/>
            <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 6"/>
            <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 6"/>
            <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 6"/>
            <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 6"/>
            <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 6"/>
            <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 6"/>
            <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 6"/>
            <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 6"/>
            <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 6"/>
            <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 6"/>
            <w:LsdException Locked="false" Priority="19" QFormat="true"
                            Name="Subtle Emphasis"/>
            <w:LsdException Locked="false" Priority="21" QFormat="true"
                            Name="Intense Emphasis"/>
            <w:LsdException Locked="false" Priority="31" QFormat="true"
                            Name="Subtle Reference"/>
            <w:LsdException Locked="false" Priority="32" QFormat="true"
                            Name="Intense Reference"/>
            <w:LsdException Locked="false" Priority="33" QFormat="true" Name="Book Title"/>
            <w:LsdException Locked="false" Priority="37" SemiHidden="true"
                            UnhideWhenUsed="true" Name="Bibliography"/>
            <w:LsdException Locked="false" Priority="39" SemiHidden="true"
                            UnhideWhenUsed="true" QFormat="true" Name="TOC Heading"/>
            <w:LsdException Locked="false" Priority="41" Name="Plain Table 1"/>
            <w:LsdException Locked="false" Priority="42" Name="Plain Table 2"/>
            <w:LsdException Locked="false" Priority="43" Name="Plain Table 3"/>
            <w:LsdException Locked="false" Priority="44" Name="Plain Table 4"/>
            <w:LsdException Locked="false" Priority="45" Name="Plain Table 5"/>
            <w:LsdException Locked="false" Priority="40" Name="Grid Table Light"/>
            <w:LsdException Locked="false" Priority="46" Name="Grid Table 1 Light"/>
            <w:LsdException Locked="false" Priority="47" Name="Grid Table 2"/>
            <w:LsdException Locked="false" Priority="48" Name="Grid Table 3"/>
            <w:LsdException Locked="false" Priority="49" Name="Grid Table 4"/>
            <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark"/>
            <w:LsdException Locked="false" Priority="51" Name="Grid Table 6 Colorful"/>
            <w:LsdException Locked="false" Priority="52" Name="Grid Table 7 Colorful"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="Grid Table 1 Light Accent 1"/>
            <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 1"/>
            <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 1"/>
            <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 1"/>
            <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 1"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="Grid Table 6 Colorful Accent 1"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="Grid Table 7 Colorful Accent 1"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="Grid Table 1 Light Accent 2"/>
            <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 2"/>
            <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 2"/>
            <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 2"/>
            <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 2"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="Grid Table 6 Colorful Accent 2"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="Grid Table 7 Colorful Accent 2"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="Grid Table 1 Light Accent 3"/>
            <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 3"/>
            <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 3"/>
            <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 3"/>
            <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 3"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="Grid Table 6 Colorful Accent 3"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="Grid Table 7 Colorful Accent 3"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="Grid Table 1 Light Accent 4"/>
            <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 4"/>
            <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 4"/>
            <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 4"/>
            <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 4"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="Grid Table 6 Colorful Accent 4"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="Grid Table 7 Colorful Accent 4"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="Grid Table 1 Light Accent 5"/>
            <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 5"/>
            <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 5"/>
            <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 5"/>
            <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 5"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="Grid Table 6 Colorful Accent 5"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="Grid Table 7 Colorful Accent 5"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="Grid Table 1 Light Accent 6"/>
            <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 6"/>
            <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 6"/>
            <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 6"/>
            <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 6"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="Grid Table 6 Colorful Accent 6"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="Grid Table 7 Colorful Accent 6"/>
            <w:LsdException Locked="false" Priority="46" Name="List Table 1 Light"/>
            <w:LsdException Locked="false" Priority="47" Name="List Table 2"/>
            <w:LsdException Locked="false" Priority="48" Name="List Table 3"/>
            <w:LsdException Locked="false" Priority="49" Name="List Table 4"/>
            <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark"/>
            <w:LsdException Locked="false" Priority="51" Name="List Table 6 Colorful"/>
            <w:LsdException Locked="false" Priority="52" Name="List Table 7 Colorful"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="List Table 1 Light Accent 1"/>
            <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 1"/>
            <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 1"/>
            <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 1"/>
            <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 1"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="List Table 6 Colorful Accent 1"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="List Table 7 Colorful Accent 1"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="List Table 1 Light Accent 2"/>
            <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 2"/>
            <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 2"/>
            <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 2"/>
            <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 2"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="List Table 6 Colorful Accent 2"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="List Table 7 Colorful Accent 2"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="List Table 1 Light Accent 3"/>
            <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 3"/>
            <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 3"/>
            <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 3"/>
            <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 3"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="List Table 6 Colorful Accent 3"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="List Table 7 Colorful Accent 3"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="List Table 1 Light Accent 4"/>
            <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 4"/>
            <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 4"/>
            <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 4"/>
            <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 4"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="List Table 6 Colorful Accent 4"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="List Table 7 Colorful Accent 4"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="List Table 1 Light Accent 5"/>
            <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 5"/>
            <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 5"/>
            <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 5"/>
            <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 5"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="List Table 6 Colorful Accent 5"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="List Table 7 Colorful Accent 5"/>
            <w:LsdException Locked="false" Priority="46"
                            Name="List Table 1 Light Accent 6"/>
            <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 6"/>
            <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 6"/>
            <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 6"/>
            <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 6"/>
            <w:LsdException Locked="false" Priority="51"
                            Name="List Table 6 Colorful Accent 6"/>
            <w:LsdException Locked="false" Priority="52"
                            Name="List Table 7 Colorful Accent 6"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Mention"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Smart Hyperlink"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Hashtag"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Unresolved Mention"/>
            <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
                            Name="Smart Link"/>
        </w:LatentStyles>
    </xml><![endif]-->
    <style>
        <!--
        /* Font Definitions */
        @font-face
        {font-family:"Cambria Math";
            panose-1:2 4 5 3 5 4 6 3 2 4;
            mso-font-charset:0;
            mso-generic-font-family:roman;
            mso-font-pitch:variable;
            mso-font-signature:-536869121 1107305727 33554432 0 415 0;}
        @font-face
        {font-family:Calibri;
            panose-1:2 15 5 2 2 2 4 3 2 4;
            mso-font-charset:161;
            mso-generic-font-family:swiss;
            mso-font-pitch:variable;
            mso-font-signature:-469750017 -1073732485 9 0 511 0;}
        @font-face
        {font-family:"Open Sans";
            panose-1:2 11 6 6 3 5 4 2 2 4;
            mso-font-charset:161;
            mso-generic-font-family:swiss;
            mso-font-pitch:variable;
            mso-font-signature:-536870161 1073750107 40 0 415 0;}
        /* Style Definitions */
        p.MsoNormal, li.MsoNormal, div.MsoNormal
        {mso-style-unhide:no;
            mso-style-qformat:yes;
            mso-style-parent:"";
            margin:0cm;
            mso-pagination:widow-orphan;
            font-size:11.0pt;
            font-family:"Calibri",sans-serif;
            mso-fareast-font-family:Calibri;
            mso-fareast-theme-font:minor-latin;}
        a:link, span.MsoHyperlink
        {mso-style-noshow:yes;
            mso-style-priority:99;
            color:#0563C1;
            text-decoration:underline;
            text-underline:single;}
        a:visited, span.MsoHyperlinkFollowed
        {mso-style-noshow:yes;
            mso-style-priority:99;
            color:#954F72;
            text-decoration:underline;
            text-underline:single;}
        p.msonormal0, li.msonormal0, div.msonormal0
        {mso-style-name:msonormal;
            mso-style-unhide:no;
            mso-margin-top-alt:auto;
            margin-right:0cm;
            mso-margin-bottom-alt:auto;
            margin-left:0cm;
            mso-pagination:widow-orphan;
            font-size:11.0pt;
            font-family:"Calibri",sans-serif;
            mso-fareast-font-family:Calibri;
            mso-fareast-theme-font:minor-latin;}
        .MsoChpDefault
        {mso-style-type:export-only;
            mso-default-props:yes;
            font-size:10.0pt;
            mso-ansi-font-size:10.0pt;
            mso-bidi-font-size:10.0pt;}
        @page WordSection1
        {size:612.0pt 792.0pt;
            margin:72.0pt 90.0pt 72.0pt 90.0pt;
            mso-header-margin:36.0pt;
            mso-footer-margin:36.0pt;
            mso-paper-source:0;}
        div.WordSection1
        {page:WordSection1;}
        -->
    </style>
    <!--[if gte mso 10]>
    <style>
        /* Style Definitions */
        table.MsoNormalTable
        {mso-style-name:"Κανονικός πίνακας";
            mso-tstyle-rowband-size:0;
            mso-tstyle-colband-size:0;
            mso-style-noshow:yes;
            mso-style-priority:99;
            mso-style-parent:"";
            mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
            mso-para-margin:0cm;
            mso-pagination:widow-orphan;
            font-size:10.0pt;
            font-family:"Times New Roman",serif;}
    </style>
    <![endif]--><!--[if gte mso 9]><xml>
        <o:shapedefaults v:ext="edit" spidmax="1026"/>
    </xml><![endif]--><!--[if gte mso 9]><xml>
        <o:shapelayout v:ext="edit">
            <o:idmap v:ext="edit" data="1"/>
        </o:shapelayout></xml><![endif]-->
</head>

<body bgcolor="#494C50" lang=EN-US link="#0563C1" vlink="#954F72"
      style='tab-interval:36.0pt;word-wrap:break-word'>

<div class=WordSection1>

    <div align=center>

        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0 width="100%"
               style='width:100.0%;mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-padding-alt:
 0cm 0cm 0cm 0cm'>
            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes'>
                <td valign=top style='background:#494C50;padding:0cm 0cm 0cm 0cm'>
                    <div align=center>
                        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0 width=550
                               style='width:412.5pt;mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-padding-alt:
   0cm 0cm 0cm 0cm'>
                            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:37.5pt'>
                                <td style='padding:0cm 0cm 0cm 0cm;height:37.5pt'></td>
                            </tr>
                            <tr style='mso-yfti-irow:1'>
                                <td width=500 style='width:375.0pt;background:white;padding:0cm 0cm 0cm 0cm'>
                                    <div align=center>
                                        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0 width="90%"
                                               style='width:90.0%;mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-padding-alt:
     0cm 0cm 0cm 0cm'>
                                            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:11.25pt'>
                                                <td style='padding:0cm 0cm 0cm 0cm;height:11.25pt'></td>
                                            </tr>
                                            <tr style='mso-yfti-irow:1'>
                                                <td style='padding:0cm 0cm 0cm 0cm'>
                                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                                           align=left style='mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-table-lspace:
       2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:paragraph;
       mso-table-anchor-horizontal:column;mso-table-left:left;mso-padding-alt:
       0cm 0cm 0cm 0cm'>
                                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes'>
                                                            <td style='padding:0cm 0cm 0cm 0cm'></td>
                                                        </tr>
                                                    </table>
                                                    <p class=MsoNormal align=center style='text-align:center'><span
                                                            style='font-size:12.0pt;font-family:"Times New Roman",serif;display:none;
      mso-hide:all'><o:p>&nbsp;</o:p></span></p>
                                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                                           align=left style='mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-table-lspace:
       2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:paragraph;
       mso-table-anchor-horizontal:column;mso-table-left:left;mso-padding-alt:
       0cm 0cm 0cm 0cm'>
                                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes;
        height:7.5pt'>
                                                            <td style='padding:0cm 0cm 0cm 0cm;height:7.5pt'></td>
                                                        </tr>
                                                    </table>
                                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                                           align=right style='mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-table-lspace:
       2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:paragraph;
       mso-table-anchor-horizontal:column;mso-table-left:right;mso-table-top:
       middle;mso-padding-alt:0cm 0cm 0cm 0cm'>
                                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes'>
                                                            <td style='padding:0cm 0cm 0cm 0cm'>
                                                                <p class=MsoNormal align=center style='text-align:center'><span
                                                                        lang=EL style='font-size:10.0pt;font-family:"Open Sans",sans-serif;
        color:#7F8C8D;mso-ansi-language:EL'>{{ $data['timestamp'] ?? '' }} </span><span
                                                                        style='font-size:10.0pt;font-family:"Open Sans",sans-serif;color:#7F8C8D'><o:p></o:p></span></p>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <p class=MsoNormal><!--[if gte vml 1]><v:shapetype id="_x0000_t75"
                                                                                                       coordsize="21600,21600" o:spt="75" o:preferrelative="t" path="m@4@5l@4@11@9@11@9@5xe"
                                                                                                       filled="f" stroked="f">
                                                            <v:stroke joinstyle="miter"/>
                                                            <v:formulas>
                                                                <v:f eqn="if lineDrawn pixelLineWidth 0"/>
                                                                <v:f eqn="sum @0 1 0"/>
                                                                <v:f eqn="sum 0 0 @1"/>
                                                                <v:f eqn="prod @2 1 2"/>
                                                                <v:f eqn="prod @3 21600 pixelWidth"/>
                                                                <v:f eqn="prod @3 21600 pixelHeight"/>
                                                                <v:f eqn="sum @0 0 1"/>
                                                                <v:f eqn="prod @6 1 2"/>
                                                                <v:f eqn="prod @7 21600 pixelWidth"/>
                                                                <v:f eqn="sum @8 21600 0"/>
                                                                <v:f eqn="prod @7 21600 pixelHeight"/>
                                                                <v:f eqn="sum @10 21600 0"/>
                                                            </v:formulas>
                                                            <v:path o:extrusionok="f" gradientshapeok="t" o:connecttype="rect"/>
                                                            <o:lock v:ext="edit" aspectratio="t"/>
                                                        </v:shapetype><v:shape id="Εικόνα_x0020_3" o:spid="_x0000_i1025" type="#_x0000_t75"
                                                                               alt="logo" style='width:123pt;height:22.5pt'>
                                                            <v:imagedata src="{{ asset('assets/img/logo.png') }}" o:href="cid:image001.png@01D886E0.E23092A0"/>
                                                        </v:shape><![endif]--><![if !vml]><img width="80"
                                                                                               src="{{ asset('assets/img/logo.png') }}" style='width:80px; height:auto;'
                                                                                               alt="logo" v:shapes="Εικόνα_x0020_3"><![endif]></p>
                                                </td>
                                            </tr>
                                            <tr style='mso-yfti-irow:2;mso-yfti-lastrow:yes;height:11.25pt'>
                                                <td style='padding:0cm 0cm 0cm 0cm;height:11.25pt'></td>
                                            </tr>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr style='mso-yfti-irow:2;height:11.25pt'>
                                <td style='padding:0cm 0cm 0cm 0cm;height:11.25pt'></td>
                            </tr>
                            <tr style='mso-yfti-irow:3'>
                                <td style='background:white;padding:0cm 0cm 0cm 0cm'>
                                    <div align=center>
                                        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0 width="90%"
                                               style='width:90.0%;mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-padding-alt:
     0cm 0cm 0cm 0cm'>
                                            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:37.5pt'>
                                                <td style='padding:0cm 0cm 0cm 0cm;height:37.5pt'></td>
                                            </tr>
                                            <tr style='mso-yfti-irow:1'>
                                                <td style='padding:0cm 0cm 0cm 0cm'>
                                                    <p class=MsoNormal align=center style='text-align:center'><b><span
                                                                lang=EL style='font-size:22.5pt;font-family:"Open Sans",sans-serif;
      mso-ansi-language:EL'>{!! $data['title'] ?? '' !!} <br>
      </span></b></p>
                                                </td>
                                            </tr>
                                            <tr style='mso-yfti-irow:2;height:15.0pt'>
                                                <td style='padding:0cm 0cm 0cm 0cm;height:15.0pt'></td>
                                            </tr>
                                            <tr style='mso-yfti-irow:3'>
                                                <td style='padding:0cm 0cm 0cm 0cm'>
                                                    <div align=center>
                                                        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                                               width=25 style='width:18.75pt;mso-cellspacing:0cm;mso-yfti-tbllook:1184;
       mso-padding-alt:0cm 0cm 0cm 0cm'>
                                                            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes;
        height:1.5pt'>
                                                                <td style='background:#a48d30;padding:0cm 0cm 0cm 0cm;height:1.5pt'></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style='mso-yfti-irow:4;height:15.0pt'>
                                                <td style='padding:0cm 0cm 0cm 0cm;height:15.0pt'></td>
                                            </tr>
                                            <tr style='mso-yfti-irow:5'>
                                                <td style='padding:0cm 0cm 0cm 0cm'>
                                                    <p class=MsoNormal align=center style='text-align:center;line-height:
      22.5pt'><span lang=EL style='font-size:14.0pt;mso-ansi-language:EL'>
                                                           {!! $data['body'] ?? '' !!}
                                                        </span>

                                                        <br>
                                                        <br>
                                                    @if($data['order'] ?? null)
                                                    <div class="container">
                                                        <div class="header">
                                                            <h3 class="MsoNormal">Order #{{ str_pad($data['order']->id, 5, '0', STR_PAD_LEFT) }}</h3>
                                                        </div>
                                                        <div class="section">
                                                            <table width="100%">
                                                                <tr>
                                                                    <td width="50%">
                                                                        <p class="MsoNormal">
                                                                            <strong>From:</strong>
                                                                            <br>
                                                                            {{ $data['order']->user->fullName }}
                                                                            <br>
                                                                            {{ $data['order']->user->companyName }}
                                                                        </p>
                                                                    </td>
                                                                    <td width="50%" class="text-right">
                                                                        @if(!$data['order']->user->hasRole('distributor'))
                                                                        <p class="MsoNormal">
                                                                            <strong>Distributor:</strong>
                                                                            <br>{{ $data['order']->user->distributor->companyName ?? '-' }}
                                                                        </p>
                                                                        @endif
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                        <br>
                                                        <div class="section">
                                                            <table class="table" cellpadding="5">
                                                                <thead>
                                                                <tr>
                                                                    <th class="center MsoNormal">#</th>
                                                                    <th class="MsoNormal">Code</th>
                                                                    <th class="MsoNormal">Item</th>
                                                                    <th class="right MsoNormal">Unit Cost</th>
                                                                    <th class="center MsoNormal">Qty</th>
                                                                    <th class="right MsoNormal">Total</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                @foreach($data['order']->orderSpareParts as $orderSparePart)
                                                                <tr>
                                                                    <td align="center" class="center MsoNormal">{{ $loop->iteration }}</td>
                                                                    <td align="center" class="MsoNormal">{{ $orderSparePart->sparePartCode }}</td>
                                                                    <td align="center" class="MsoNormal">{{ $orderSparePart->sparePartTitle }}</td>
                                                                    <td align="center" class="right MsoNormal">{{ number_format($orderSparePart->price / $orderSparePart->quantity, 2, '.', '') }}€</td>
                                                                    <td align="center" class="center MsoNormal">{{ $orderSparePart->quantity }}</td>
                                                                    <td align="center" class="right MsoNormal">{{ number_format($orderSparePart->price, 2, '.', '') }}€</td>
                                                                </tr>
                                                                @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                        <div class="section">
                                                            <table class="table total">
                                                                <tr>
                                                                    <td width="450" align="right" class="MsoNormal"><strong>Total:</strong> </td>
                                                                    <td width="100" align="center" class="MsoNormal">{{ number_format($data['order']->totalPrice, 2, '.', '') }}€</td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    @endif
                                                        <span
                                                            lang=EL style='font-size:14.0pt;mso-ansi-language:EL'><o:p></o:p></span></p>
                                                </td>
                                            </tr>
                                            <tr style='mso-yfti-irow:6;mso-yfti-lastrow:yes;height:30.0pt'>
                                                <td style='padding:0cm 0cm 0cm 0cm;height:30.0pt'></td>
                                            </tr>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            <tr style='mso-yfti-irow:4;mso-yfti-lastrow:yes'>
                                <td style='background:#ECF0F1;padding:0cm 0cm 0cm 0cm'>
                                    <div align=center>
                                        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0 width="90%"
                                               style='width:90.0%;mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-padding-alt:
     0cm 0cm 0cm 0cm'>
                                            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:22.5pt'>
                                                <td style='padding:0cm 0cm 0cm 0cm;height:22.5pt'></td>
                                            </tr>
                                            <tr style='mso-yfti-irow:1'>
                                                <td style='padding:0cm 0cm 0cm 0cm'>
                                                    <div align=center>
                                                        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                                               style='mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
                                                            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes;
        height:41.25pt'>
                                                                <td style='background:#a48d30;padding:0cm 18.75pt 0cm 18.75pt;
        height:41.25pt'>
                                                                    <p class=MsoNormal align=center style='text-align:center'><b><span
                                                                                lang=EL style='font-size:12.0pt;font-family:"Open Sans",sans-serif;
        color:white;mso-ansi-language:EL'><a style='color:white;
        text-decoration:none;text-underline:none'
                                             href="{!! $data['ctaLink'] ?? '' !!}"><span >{!! $data['ctaText'] ?? '' !!}</span></a> </span></b><b><span
                                                                                style='font-size:12.0pt;font-family:"Open Sans",sans-serif;color:white'><o:p></o:p></span></b></p>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr style='mso-yfti-irow:2;mso-yfti-lastrow:yes;height:22.5pt'>
                                                <td style='padding:0cm 0cm 0cm 0cm;height:22.5pt'></td>
                                            </tr>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <p class=MsoNormal><span style='font-size:12.0pt;font-family:"Times New Roman",serif;
  display:none;mso-hide:all'><o:p>&nbsp;</o:p></span></p>
                    <div align=center>
                        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0 width=550
                               style='width:412.5pt;mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-padding-alt:
   0cm 0cm 0cm 0cm'>
                            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:11.25pt'>
                                <td style='padding:0cm 0cm 0cm 0cm;height:11.25pt'></td>
                            </tr>
                            <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
                                <td style='padding:0cm 0cm 0cm 0cm'>
                                    <p class=MsoNormal style='text-align:justify'><span lang=EL
                                                                                        style='font-size:10.0pt;color:#D9D9D9;mso-ansi-language:EL'>Αυτό το email στάλθηκε από ένα αυτοματοποιημένο σύστημα. Μην απαντάτε απευθείας σε αυτό το email. Εάν έχετε οποιεσδήποτε ερωτήσεις ή χρειάζεστε βοήθεια, μην διστάσετε να επικοινωνήσετε με την ομάδα υποστήριξης.</span></p>
                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                           align=left width=15 style='width:11.25pt;mso-cellspacing:0cm;mso-yfti-tbllook:
     1184;mso-table-lspace:2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:
     paragraph;mso-table-anchor-horizontal:column;mso-table-left:left;
     mso-padding-alt:0cm 0cm 0cm 0cm'>
                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes;
      height:11.25pt'>
                                            <td style='padding:0cm 0cm 0cm 0cm;height:11.25pt'></td>
                                        </tr>
                                    </table>
                                    <p class=MsoNormal align=center style='text-align:center'><span lang=EL
                                                                                                    style='font-size:12.0pt;font-family:"Times New Roman",serif;display:none;
    mso-hide:all;mso-ansi-language:EL'><o:p>&nbsp;</o:p></span></p>
                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                           align=left width=173 style='width:129.75pt;mso-cellspacing:0cm;mso-yfti-tbllook:
     1184;mso-table-lspace:2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:
     paragraph;mso-table-anchor-horizontal:column;mso-table-left:left;
     mso-padding-alt:0cm 0cm 0cm 0cm'>
                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes'>
                                            <td style='padding:0cm 0cm 0cm 0cm'></td>
                                        </tr>
                                    </table>
                                    <p class=MsoNormal align=center style='text-align:center'><span lang=EL
                                                                                                    style='font-size:12.0pt;font-family:"Times New Roman",serif;display:none;
    mso-hide:all;mso-ansi-language:EL'><o:p>&nbsp;</o:p></span></p>
                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                           align=left width=15 style='width:11.25pt;mso-cellspacing:0cm;mso-yfti-tbllook:
     1184;mso-table-lspace:2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:
     paragraph;mso-table-anchor-horizontal:column;mso-table-left:left;
     mso-padding-alt:0cm 0cm 0cm 0cm'>
                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes;
      height:11.25pt'>
                                            <td style='padding:0cm 0cm 0cm 0cm;height:11.25pt'></td>
                                        </tr>
                                    </table>
                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                           align=right width=173 style='width:129.75pt;mso-cellspacing:0cm;
     mso-yfti-tbllook:1184;mso-table-lspace:2.25pt;mso-table-rspace:2.25pt;
     mso-table-anchor-vertical:paragraph;mso-table-anchor-horizontal:column;
     mso-table-left:right;mso-table-top:middle;mso-padding-alt:0cm 0cm 0cm 0cm'>
                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes'>
                                            <td style='padding:0cm 0cm 0cm 0cm'></td>
                                        </tr>
                                    </table>
                                    <p class=MsoNormal><span lang=EL style='mso-ansi-language:EL'><o:p></o:p></span></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <p class=MsoNormal><span lang=EL style='font-size:12.0pt;font-family:"Times New Roman",serif;
  display:none;mso-hide:all;mso-ansi-language:EL'><o:p>&nbsp;</o:p></span></p>
                    <div align=center>
                        <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0 width=550
                               style='width:412.5pt;mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-padding-alt:
   0cm 0cm 0cm 0cm'>
                            <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:11.25pt'>
                                <td style='padding:0cm 0cm 0cm 0cm;height:11.25pt'></td>
                            </tr>
                            <tr style='mso-yfti-irow:1'>
                                <td style='padding:0cm 0cm 0cm 0cm'>
                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                           align=left style='mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-table-lspace:
     2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:paragraph;
     mso-table-anchor-horizontal:column;mso-table-left:left;mso-padding-alt:
     0cm 0cm 0cm 0cm'>
                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes'>
                                            <td style='padding:0cm 0cm 0cm 0cm'>
                                                <p class=MsoNormal align=center style='text-align:center'><span
                                                        style='color:#D9D9D9'>© {{ date('Y') }} {{ env('DOMAIN', 'politiq.app') }} All Rights Reserved </span><span
                                                        style='font-size:9.0pt;color:#D9D9D9'><o:p></o:p></span></p>
                                            </td>
                                        </tr>
                                    </table>
                                    <p class=MsoNormal align=center style='text-align:center'><span
                                            style='font-size:12.0pt;font-family:"Times New Roman",serif;color:#D9D9D9;
    display:none;mso-hide:all'><o:p>&nbsp;</o:p></span></p>
                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                           align=left width=15 style='width:11.25pt;mso-cellspacing:0cm;mso-yfti-tbllook:
     1184;mso-table-lspace:2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:
     paragraph;mso-table-anchor-horizontal:column;mso-table-left:left;
     mso-padding-alt:0cm 0cm 0cm 0cm'>
                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes;
      height:11.25pt'>
                                            <td style='padding:0cm 0cm 0cm 0cm;height:11.25pt'></td>
                                        </tr>
                                    </table>
                                    <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
                                           align=right style='mso-cellspacing:0cm;mso-yfti-tbllook:1184;mso-table-lspace:
     2.25pt;mso-table-rspace:2.25pt;mso-table-anchor-vertical:paragraph;
     mso-table-anchor-horizontal:column;mso-table-left:right;mso-table-top:
     middle;mso-padding-alt:0cm 0cm 0cm 0cm'>
                                        <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;mso-yfti-lastrow:yes'>
                                            <td style='padding:0cm 0cm 0cm 0cm'></td>
                                        </tr>
                                    </table>
                                    <p class=MsoNormal align=right style='text-align:right'><span
                                            style='color:#D9D9D9'><a href="mailto:info{{ '@' . env('DOMAIN', 'politiq.app') }}" style='color:white;
        text-decoration:none;text-underline:none'><span
                                                    style='color:#D9D9D9;text-decoration:none;text-underline:none'>info{{ '@' . env('DOMAIN', 'politiq.app') }}</span></a><o:p></o:p></span></p>
                                </td>
                            </tr>
                            <tr style='mso-yfti-irow:2;mso-yfti-lastrow:yes;height:33.75pt'>
                                <td style='padding:0cm 0cm 0cm 0cm;height:33.75pt'></td>
                            </tr>
                        </table>
                    </div>
                    <p class=MsoNormal align=center style='text-align:center'><o:p></o:p></p>
                </td>
            </tr>
        </table>

    </div>

    <p class=MsoNormal align=center style='text-align:center'><span lang=EL
                                                                    style='mso-ansi-language:EL'><o:p>&nbsp;</o:p></span></p>

</div>

</body>

</html>
