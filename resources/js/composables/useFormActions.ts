import { router } from '@inertiajs/vue3';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import Swal from 'sweetalert2';

interface FormConfig {
    entityName: string; // e.g., 'tag', 'room'
    addRoute: string; // e.g., 'addTagPost', 'addRoomPost'
    updateRoute: string; // e.g., 'updateTagPost', 'updateRoomPost'
    addSuccessMessage: string; // e.g., 'alerts.tags.add.success'
    updateSuccessMessage: string; // e.g., 'alerts.tags.update.success'
    indexRoute?: string; // Optional: redirect route after success
}

interface FormCallbacks<T> {
    prepareAddPayload: (formData: T) => any;
    prepareUpdatePayload: (formData: T, item: any) => any;
    onAddSuccess?: (response?: any) => void;
    onUpdateSuccess?: (response?: any) => void;
    onAddError?: (errors: any) => void;
    onUpdateError?: (errors: any) => void;
}

export function useFormActions<T>(
    formData: () => T,
    existingItem: any,
    config: FormConfig,
    callbacks: FormCallbacks<T>
) {
    const { t } = useI18n();

    const editMode = ref(!!existingItem);
    const isSubmitting = ref(false);

    const handleAdd = async () => {
        if (isSubmitting.value) return;

        isSubmitting.value = true;

        try {
            const payload = callbacks.prepareAddPayload(formData());

            router.post(route(config.addRoute), payload, {
                onSuccess: (response) => {
                    Swal.fire(
                        t('alerts.common.add.successTitle'),
                        t(config.addSuccessMessage),
                        'success'
                    );

                    // Call custom success callback if provided
                    if (callbacks.onAddSuccess) {
                        callbacks.onAddSuccess(response);
                    }

                    // Redirect to index if specified
                    if (config.indexRoute) {
                        router.visit(route(config.indexRoute));
                    }
                },
                onError: (errors) => {
                    console.error(`Error adding ${config.entityName}:`, errors);

                    // Call custom error callback if provided
                    if (callbacks.onAddError) {
                        callbacks.onAddError(errors);
                    } else {
                        // Default error handling
                        Swal.fire(
                            t('alerts.common.add.errorTitle'),
                            t(`alerts.${config.entityName}s.add.error`) || 'An error occurred',
                            'error'
                        );
                    }
                },
                onFinish: () => {
                    isSubmitting.value = false;
                }
            });
        } catch (error) {
            console.error(`Error in handleAdd for ${config.entityName}:`, error);
            isSubmitting.value = false;
        }
    };

    const handleUpdate = async () => {
        if (isSubmitting.value || !existingItem) return;

        isSubmitting.value = true;

        try {
            const payload = callbacks.prepareUpdatePayload(formData(), existingItem);

            router.post(route(config.updateRoute, { id: existingItem.id }), payload, {
                onSuccess: (response) => {
                    Swal.fire(
                        t('alerts.common.update.successTitle'),
                        t(config.updateSuccessMessage),
                        'success'
                    );

                    // Call custom success callback if provided
                    if (callbacks.onUpdateSuccess) {
                        callbacks.onUpdateSuccess(response);
                    }

                    // Redirect to index if specified
                    /*if (config.indexRoute) {
                        router.visit(route(config.indexRoute));
                    }*/
                },
                onError: (errors) => {
                    console.error(`Error updating ${config.entityName}:`, errors);

                    // Call custom error callback if provided
                    if (callbacks.onUpdateError) {
                        callbacks.onUpdateError(errors);
                    } else {
                        // Default error handling
                        Swal.fire(
                            t('alerts.common.update.errorTitle'),
                            t(`alerts.${config.entityName}s.update.error`) || 'An error occurred',
                            'error'
                        );
                    }
                },
                onFinish: () => {
                    isSubmitting.value = false;
                }
            });
        } catch (error) {
            console.error(`Error in handleUpdate for ${config.entityName}:`, error);
            isSubmitting.value = false;
        }
    };

    const handleSubmit = () => {
        if (editMode.value) {
            handleUpdate();
        } else {
            handleAdd();
        }
    };

    return {
        // State
        editMode,
        isSubmitting,

        // Methods
        handleAdd,
        handleUpdate,
        handleSubmit,
    };
}
