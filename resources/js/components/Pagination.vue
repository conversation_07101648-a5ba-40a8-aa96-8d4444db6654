<template>
    <b-pagination
        v-model="currentPage"
        :total-rows="items.total"
        :per-page="items.per_page"
        :first-text="t('pagination.first')"
        :prev-text="t('pagination.previous')"
        :next-text="t('pagination.next')"
        :last-text="t('pagination.last')"
        @update:model-value="(value) => goToPage(value as number)"
        class="justify-content-end mb-0 gap-1"
    />
</template>
<script setup lang="ts">
import { BPagination } from 'bootstrap-vue-next';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { ref, watch } from 'vue';
import { router } from '@inertiajs/vue3';

const props = defineProps<{
    items: any;
}>();
const currentPage = ref(props.items.current_page);
// Go to page handler
const goToPage = (page: number) => {
    if (page !== props.items.current_page) {
        const targetLink = props.items.links.find((link:any) => link.label === page.toString() && link.url !== null);

        if (targetLink?.url) {
            router.visit(targetLink.url);
        }
    }
};
// Sync local currentPage with props
watch(
    () => props.items.current_page,
    (val) => {
        currentPage.value = val;
    },
);
</script>
