<template>
    <div class="position-relative">
        <input type="text" class="form-control ps-4 pe-4" v-model="search" :placeholder="placeholder" />
        <i class="ti ti-search position-absolute translate-middle-y top-50 ms-2"></i>
        <i
            v-if="search?.length"
            @click="clear"
            class="pq-cursor-pointer ti ti-x position-absolute translate-middle-y end-0 top-50 p-2"
        ></i>
    </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { router } from '@inertiajs/vue3';
import { defineProps, defineModel } from 'vue';

const props = defineProps<{
    initialValue: string;
    placeholder: string;
    searchRoute: string;
}>();

const search = defineModel<string>('initialValue');

let searchTimeout: number;
watch(search, (newValue) => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        router.post(route(props.searchRoute), { searchKey: newValue });
    }, 500);
});

const clear = () => {
    search.value = '';
};
</script>
