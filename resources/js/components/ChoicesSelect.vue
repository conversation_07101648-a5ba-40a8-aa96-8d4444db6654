<template>
    <label v-if="label" :for="id" class="form-label" :class="labelClass">{{ label }}</label>
    <select :id="id" :value="modelValue" v-bind="$attrs" class="form-select" :multiple="multiple">
        <slot />
        <template v-if="options">
            <option v-for="(option, idx) in options" :key="idx" :value="option.value">
                {{ option.text }}
            </option>
        </template>
    </select>
</template>

<script setup lang="ts">
import Choices, { type Options } from 'choices.js';
import { nextTick, onMounted } from 'vue';

type PropsType = {
    id: string;
    label?: string;
    labelClass?: string;
    modelValue?: any;
    options?: { value: string | number | null; text: string }[];
    choiceOptions?: Partial<Options>;
    multiple?: boolean;
};
const props = defineProps<PropsType>();

const emit = defineEmits(['update:modelValue']);

const updateValue = (e: Event) => {
    const target = e.target as HTMLSelectElement;
    if (props.multiple) {
        const values = Array.from(target.selectedOptions).map((option) => {
            const value = option.value;
            // Convert to number if it's a numeric string, otherwise keep as sring
            return /^\d+$/.test(value) ? Number(value) : value;
        });
        emit('update:modelValue', values);
    } else {
        emit('update:modelValue', target.value);
    }
};

onMounted(async () => {
    await nextTick(); // Wait for DOM to be fully rendered

    const selectElement = document.querySelector(`#${props.id}`) as HTMLSelectElement;
    const choices = new Choices(selectElement, {
        ...props.choiceOptions,
    });

    // Set initial values after Choices.js initialization and DOM update
    setTimeout(() => {
        if (props.modelValue !== undefined && props.modelValue !== null) {
            if (props.multiple && Array.isArray(props.modelValue)) {
                choices.setChoiceByValue(props.modelValue.map(String));
            } else if (!props.multiple) {
                choices.setChoiceByValue(String(props.modelValue));
            }
        }
    }, 100);

    // Listen to Choices.js change event
    selectElement.addEventListener('change', (e) => {
        if (props.multiple) {
            const selectedValues = choices.getValue(true);
            const values = Array.isArray(selectedValues) ? selectedValues.map((val) => (/^\d+$/.test(String(val)) ? Number(val) : val)) : [];
            emit('update:modelValue', values);
        } else {
            const value = choices.getValue(true);
            emit('update:modelValue', /^\d+$/.test(String(value)) ? Number(value) : value);
        }
    });
});
</script>
