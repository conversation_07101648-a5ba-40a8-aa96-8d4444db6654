<template>
    <Ckeditor :editor="ClassicEditor" v-model="localValue" :config="editorConfig" />
</template>

<script setup lang="ts">
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import { ref, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['update:modelValue']);

// Local state for v-model binding
const localValue = ref(props.modelValue);

watch(
    () => props.modelValue,
    (newVal) => {
        if (localValue.value !== newVal) localValue.value = newVal;
    },
);

watch(localValue, (newVal) => {
    emit('update:modelValue', newVal);
});

// Optional: customize toolbar/config
const editorConfig = {
    toolbar: ['undo', 'redo', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList'],
};
</script>
