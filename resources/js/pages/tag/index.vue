<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-tags" :title="t('tags.title')" :subtitle="t('tags.list')" subtitleRoute="tagIndex" />

        <b-row>
            <b-col cols="12">
                <b-card no-body>
                    <b-card-header class="border-bottom border-light">
                        <div class="d-flex justify-content-between flex-wrap gap-2">
                            <SearchInput :initialValue="searchInput" searchRoute="tagIndexPost" :placeholder="t('tags.search')" />
                            <div>
                                <Link :href="route('tagAdd')" class="btn btn-primary"> <i class="ti ti-plus me-1"></i>{{ t('tags.add') }} </Link>
                            </div>
                        </div>
                    </b-card-header>

                    <b-table-simple responsive hover class="mb-0 text-nowrap">
                        <b-thead class="bg-light-subtle">
                            <b-tr>
                                <b-th class="ps-3" style="width: 50px">
                                    <b-form-checkbox :checked="allSelected" @change="toggleSelectAll" />
                                </b-th>
                                <b-th>ID</b-th>
                                <b-th><i class="ti ti-article fs-16 me-1 align-text-top" />{{ t('common.title') }}</b-th>
                                <b-th><i class="ti ti-shield fs-16 me-1 align-text-top" />{{ t('common.system') }}</b-th>
                                <b-th class="text-center" style="width: 120px"
                                    ><i class="ti ti-bolt fs-16 me-1 align-text-top" />{{ t('common.actions') }}</b-th
                                >
                            </b-tr>
                        </b-thead>
                        <b-tbody>
                            <b-tr v-for="tag in tags.data" :key="`tag_${tag.id}`">
                                <b-td class="ps-3">
                                    <b-form-checkbox :value="tag.id" v-model="selectedRows" />
                                </b-td>
                                <b-td>{{ tag.id }}</b-td>
                                <b-td>
                                    <Link variant="info" v-b-tooltip.hover.top="t('tags.edit')" :href="route('updateTag', { id: tag.id })">{{
                                        tag.title
                                    }}</Link>
                                </b-td>
                                <b-td>{{ tag.system ? t('common.yes') : t('common.no') }}</b-td>

                                <b-td class="pe-3">
                                    <div class="hstack justify-content-end gap-1">
                                        <template v-if="!tag.system">
                                            <Link
                                                :href="route('updateTag', { id: tag.id })"
                                                class="btn btn-soft-success btn-icon btn-sm rounded-circle"
                                                ><i class="ti ti-edit fs-16"></i
                                            ></Link>

                                            <button @click="deleteWithConfirm(tag.id)" class="btn btn-soft-danger btn-icon btn-sm rounded-circle">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        </template>
                                        <span v-else class="badge bg-danger rounded-pill me-1">{{ t('tags.system') }}</span>
                                    </div>
                                </b-td>
                            </b-tr>
                        </b-tbody>
                    </b-table-simple>

                    <b-card-footer class="d-flex justify-content-between align-items-center">
                        <div class="">
                            <button
                                v-if="selectedRows.length"
                                v-b-tooltip.hover.top="t('common.bulkDelete')"
                                @click="deleteMultipleWithConfirm"
                                class="btn btn-soft-danger btn-icon btn-sm rounded-circle"
                            >
                                <i class="ti ti-trash"></i>
                            </button>
                        </div>

                        <Pagination
                            :items="props.tags"
                        />

                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { useDeleteActions } from '@/composables/useDeleteActions';
import type { Tag } from '@/types/tag';
import { Link, usePage } from '@inertiajs/vue3';
import {
    BCard, BCardFooter,
    BCardHeader,
    BCol,
    BFormCheckbox,
    BRow,
    BTableSimple,
    BTbody, BTd,
    BTh,
    BThead,
    BTr,
    vBTooltip
} from 'bootstrap-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import Pagination from '@/components/Pagination.vue';
import SearchInput from '@/components/SearchInput.vue';

const { t } = useI18n();


// Define props passed from Laravel
const props = defineProps<{
    searchKey?: string;
    tags: {
        data: Tag[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();

// Delete Actions Composable
const {
    selectedRows,
    allSelected,
    toggleSelectAll,
    deleteWithConfirm,
    deleteMultipleWithConfirm
} = useDeleteActions(
    () => props.tags.data,
    {
        entityName: 'tag',
        deleteRoute: 'deleteTag',
        massDeleteRoute: 'massDeleteTags',
        successMessage: 'alerts.tags.delete.success',
        errorMessage: 'alerts.tags.delete.error',
        bulkSuccessMessage: 'alerts.tags.delete.success',
        bulkErrorMessage: 'alerts.tags.delete.error',
        bulkDeleteTranslationKey: 'tags.bulkDelete'
    }
);

// Search Input
const searchInput = ref(usePage().props.searchKey as string || '');

</script>
