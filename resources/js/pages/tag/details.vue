<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-tag" :title="editMode ? t('tags.edit') : t('tags.add')" :subtitle="t('tags.list')" subtitleRoute="tagIndex" />

        <b-row>
            <b-col lg="7">
                <b-card no-body>
                    <b-card-body>
                        <b-row>
                            <b-col lg="12">
                                <b-form-group :label="t('common.title')" label-for="title" class="mb-3">
                                    <b-form-input type="text" v-model="tagName" name="title" :placeholder="t('tags.editPlaceholder')" />
                                </b-form-group>
                            </b-col>
                            <div class="d-flex justify-content-between flex-wrap gap-2 mt-3">
                                <Link :href="route('tagIndex')" class="btn btn-secondary">
                                    <i class="ti ti-arrow-left me-1"></i>{{ t('common.backToList') }}</Link
                                >
                                <b-button variant="primary" @click="handleSubmit" type="submit" :disabled="isSubmitting">
                                    <b-spinner v-if="isSubmitting" small class="me-1" />
                                    <i v-else class="ti ti-device-floppy me-1"></i>
                                    {{ editMode ? t('common.update') : t('common.save') }}
                                </b-button>
                            </div>
                        </b-row>
                    </b-card-body>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { useFormActions } from '@/composables/useFormActions';
import type { Tag } from '@/types/tag';
import { Link, usePage } from '@inertiajs/vue3';
import { BButton, BCard, BCardBody, BCol, BFormGroup, BFormInput, BRow, BSpinner } from 'bootstrap-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = usePage<{ tag?: Tag }>().props;
const tag: Tag | null = props.tag ?? null;

const tagName = ref(tag ? tag.title : '');

// Use the form actions composable
const {
    editMode,
    isSubmitting,
    handleSubmit
} = useFormActions(
    () => ({ title: tagName.value }),
    tag,
    {
        entityName: 'tag',
        addRoute: 'addTagPost',
        updateRoute: 'updateTagPost',
        addSuccessMessage: 'alerts.tags.add.success',
        updateSuccessMessage: 'alerts.tags.update.success',
        indexRoute: 'tagIndex'
    },
    {
        prepareAddPayload: (formData) => ({
            title: formData.title
        }),
        prepareUpdatePayload: (formData, existingTag) => ({
            id: existingTag.id,
            title: formData.title
        }),
        onAddSuccess: () => {
            // Clear the form after successful add
            tagName.value = '';
        }
    }
);
</script>
