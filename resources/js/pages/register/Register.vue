<template>
    <AuthLayout show-footer>
        <Head title="Log in" />

        <h3 class="fw-semibold mb-2">Register your account</h3>

        <p class="text-muted mb-4">Placeholder text.</p>

        <p v-if="status" class="text-success mb-3">
            {{ status }}
        </p>

        <b-form @submit.prevent="submit" class="mb-3 text-start">
            <div v-if="error.length > 0" class="text-danger mb-2">{{ error }}</div>

            <b-form-group label="FirstName" class="mb-3">
                <b-form-input type="text" id="name" name="name" v-model="form.name" placeholder="Enter your name" />
                <p v-if="form.errors.email" class="text-danger">
                    {{ form.errors.name }}
                </p>
            </b-form-group>

            <b-form-group label="LastName" class="mb-3">
                <b-form-input type="text" id="surname" name="surname" v-model="form.surname" placeholder="Enter your surname" />
                <p v-if="form.errors.email" class="text-danger">
                    {{ form.errors.surname }}
                </p>
            </b-form-group>

            <b-form-group label="Email" class="mb-3">
                <b-form-input type="email" id="email" name="email" v-model="form.email" placeholder="Enter your email" />
                <p v-if="form.errors.email" class="text-danger">
                    {{ form.errors.email }}
                </p>
            </b-form-group>

            <b-form-group label="Password" class="mb-3">
                <b-form-input type="password" id="password" name="password" v-model="form.password" placeholder="Enter your password" />
                <p v-if="form.errors.password" class="text-danger">
                    {{ form.errors.password }}
                </p>
            </b-form-group>

            <b-form-group label="Password" class="mb-3">
                <b-form-input type="password" id="password2" name="password2" v-model="form.password" placeholder="Verify your password" />
                <p v-if="form.errors.password" class="text-danger">
                    {{ form.errors.password }}
                </p>
            </b-form-group>

            <div class="d-grid">
                <b-button variant="primary" type="submit" :disabled="form.processing">Register</b-button>
            </div>
        </b-form>

        <p class="text-danger fs-14 mb-4">
            <Link :href="route('login')" class="fw-semibold text-dark ms-1">{{ t('auth.forgotPassword.backToLogin') }}</Link>
        </p>
    </AuthLayout>
</template>

<script setup lang="ts">
import AuthLayout from '@/layouts/AuthLayout.vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { BButton, BForm, BFormGroup, BFormInput } from 'bootstrap-vue-next';
const { t } = useI18n();

const props = usePage().props;

defineProps<{
    status?: string;
    canResetPassword: boolean;
}>();

const error = ref('');

const form = useForm({
    name: 'Michael',
    surname: 'Scott',
    email: '<EMAIL>',
    password: 'password',
});

const submit = () => {
    form.post(route('registerPost'), {
        onFinish: () => form.reset('password'),
    });
};
</script>
