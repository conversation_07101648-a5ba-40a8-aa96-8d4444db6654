<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-door" :title="t('procedures.title')" :subtitle="t('procedures.list')" subtitleRoute="procedureIndex"/>

        <b-row>
            <b-col cols="12">
                <b-card no-body>
                    <b-card-header class="border-bottom border-light">
                        <div class="d-flex justify-content-between flex-wrap gap-2">
                            <SearchInput :initialValue="searchInput" searchRoute="procedureIndexPost"
                                         :placeholder="t('procedures.search')"/>
                            <select>
                                <option value="" :selected="activePeriod === ''">All Procedures</option>
                                <option value="1">Active Only</option>
                                <option value="0">Expired Only</option>
                            </select>
                            <div>
                                <Link :href="route('procedureAdd')" class="btn btn-primary"><i
                                    class="ti ti-plus me-1"></i>{{ t('procedures.add') }}
                                </Link>
                            </div>
                        </div>
                    </b-card-header>

                    <b-table-simple responsive hover class="mb-0 text-nowrap">
                        <b-thead class="bg-light-subtle">
                            <b-tr>
                                <b-th class="ps-3" style="width: 50px">
                                    <b-form-checkbox :checked="allSelected" @change="toggleSelectAll"/>
                                </b-th>
                                <b-th>ID</b-th>
                                <b-th><i class="ti ti-article fs-16 me-1 align-text-top"/>{{ t('common.title') }}</b-th>
                                <b-th><i class="ti ti-clock fs-16 me-1 align-text-top"/>{{ t('common.starts_at') }}</b-th>
                                <b-th><i class="ti ti-clock fs-16 me-1 align-text-top"/>{{ t('common.ends_at') }}</b-th>
                                <b-th class="text-center"><i class="ti ti-home-2 fs-16 me-1 align-text-top"/>{{ t('rooms.title') }}</b-th>
                                <b-th class="text-center" style="width: 120px"
                                ><i class="ti ti-bolt fs-16 me-1 align-text-top"/>{{ t('common.actions') }}
                                </b-th
                                >
                            </b-tr>
                        </b-thead>
                        <b-tbody>
                            <b-tr v-for="procedure in procedures.data" :key="`procedure_${procedure.id}`">
                                <b-td class="ps-3">
                                    <b-form-checkbox :value="procedure.id" v-model="selectedRows"/>
                                </b-td>
                                <b-td>{{ procedure.id }}</b-td>
                                <b-td>
                                    <Link variant="info" v-b-tooltip.hover.top="t('procedures.edit')"
                                          :href="route('updateProcedure', { id: procedure.id })">{{
                                            procedure.title
                                        }}
                                    </Link>
                                </b-td>
                                <b-td>{{ procedure.starts_at }}</b-td>
                                <b-td>{{ procedure.ends_at }}</b-td>
                                <b-td class="text-center">{{ procedure.rooms.length }}</b-td>

                                <b-td class="pe-3">
                                    <div class="hstack justify-content-end gap-1">
                                        <Link :href="route('updateProcedure', { id: procedure.id })"
                                              class="btn btn-soft-success btn-icon btn-sm rounded-circle"
                                        ><i class="ti ti-edit fs-16"></i
                                        ></Link>

                                        <button @click="deleteWithConfirm(procedure.id)"
                                                class="btn btn-soft-danger btn-icon btn-sm rounded-circle">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>
                                </b-td>
                            </b-tr>
                        </b-tbody>
                    </b-table-simple>

                    <b-card-footer>
                        <div class="">
                            <button
                                v-if="selectedRows.length"
                                v-b-tooltip.hover.top="`TODO:${t('common.bulkDelete')}`"
                                @click="deleteMultipleWithConfirm"
                                class="btn btn-soft-danger btn-icon btn-sm rounded-circle"
                            >
                                <i class="ti ti-trash"></i>
                            </button>
                        </div>
                        <Pagination
                            :items="props.procedures"
                        />
                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import {useDeleteActions} from '@/composables/useDeleteActions';
import {Procedure} from '@/types/procedure';
import {Link, usePage} from '@inertiajs/vue3';
import {
    BCard,
    BCardFooter,
    BCardHeader,
    BCol,
    BFormCheckbox,
    BRow,
    BTableSimple,
    BTbody,
    BTd,
    BTh,
    BThead,
    BTr,
    vBTooltip
} from 'bootstrap-vue-next';
import {ref} from 'vue';
import {useI18n} from 'vue-i18n';
import Pagination from '@/components/Pagination.vue';
import SearchInput from '@/components/SearchInput.vue';

const {t} = useI18n();

// Define props passed from Laravel
const props = defineProps<{
    searchKey?: string;
    procedures: {
        data: Procedure[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();

// Delete Actions Composable
const {
    selectedRows,
    allSelected,
    toggleSelectAll,
    deleteWithConfirm,
    deleteMultipleWithConfirm
} = useDeleteActions(
    () => props.procedures.data,
    {
        entityName: 'procedure',
        deleteRoute: 'deleteProcedure',
        massDeleteRoute: 'massDeleteProcedures',
        successMessage: 'alerts.procedures.delete.success',
        errorMessage: 'alerts.procedures.delete.error',
        bulkSuccessMessage: 'alerts.procedures.delete.successBulk',
        bulkErrorMessage: 'alerts.procedures.delete.errorBulk',
        bulkDeleteTranslationKey: 'procedures.bulkDelete'
    }
);

// Search Input
const searchInput = ref(usePage().props.searchKey as string || '');
const activePeriod = ref(usePage().props.activePeriod as string || '');

</script>
