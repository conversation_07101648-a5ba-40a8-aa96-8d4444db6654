<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-door" :title="editMode ? t('procedures.edit') : t('procedures.add')" :subtitle="t('procedures.list')"
                   subtitleRoute="procedureIndex"/>

        <b-card no-body>
            <b-card-body>
                <b-row>
                    <b-col xl="6" lg="12">
                        <b-form-group label-for="title" class="mb-3">
                            <template #label><i class="ti ti-article fs-16 me-1 align-text-top"/>{{ t('common.title') }}</template>
                            <b-form-input v-model="form.title" type="text" name="title" :placeholder="t('rooms.editPlaceholder')"/>
                        </b-form-group>
                    </b-col>
                    <b-col xl="6" lg="12">

                    </b-col>
                </b-row>
            </b-card-body>
        </b-card>

        <b-card no-body>
            <div class="d-flex justify-content-center flex-wrap gap-2 p-2">
                <button @click="handleSubmit" type="submit" class="btn btn-primary" :disabled="isSubmitting">
                    {{ editMode ? t('common.update') : t('common.save') }}
                </button>
                <Link :href="route('procedureIndex')" class="btn btn-secondary"><i
                    class="ti ti-arrow-left me-1"></i>{{ t('common.backToList') }}
                </Link>
            </div>
        </b-card>
        <b-card no-body>
            <b-card-body>
                <b-form-group :label="t('common.description')" label-for="description" class="mb-3">
                    <template #label><i class="ti ti-file-description fs-16 me-1 align-text-top"/>{{ t('common.description') }}</template>
                    <Editor v-model="form.description"/>
                </b-form-group>
            </b-card-body>
        </b-card>
    </VerticalLayout>
</template>

<script setup lang="ts">
import Editor from '@/components/Editor.vue';
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import {useFormActions} from '@/composables/useFormActions';
import {Procedure, ProcedureFormData} from '@/types/procedure';
import {Room} from '@/types/room';
import {Link} from '@inertiajs/vue3';
import {BCard, BCardBody, BCol, BFormGroup, BRow} from 'bootstrap-vue-next';
import {computed, PropType, reactive} from 'vue';
import {useI18n} from 'vue-i18n';

const {t} = useI18n();

// Props
const props = defineProps({
    procedure: {
        type: Object as PropType<Procedure>,
        required: false,
    },
    rooms: {
        type: Array as PropType<Room[]>,
        required: false,
    },
});

// Initialize form data with existing room data
const form = reactive<ProcedureFormData>({
    title: props.procedure?.title ?? '',
    description: props.procedure?.description ?? '',
    rooms: props.procedure?.rooms ?? [],
});

// Use the form actions composable
const {
    editMode,
    isSubmitting,
    handleSubmit
} = useFormActions(
    () => form,
    props.procedure,
    {
        entityName: 'procedure',
        addRoute: 'addProcedurePost',
        updateRoute: 'updateProcedurePost',
        addSuccessMessage: 'alerts.procedures.add.success',
        updateSuccessMessage: 'alerts.procedures.update.success',
        indexRoute: 'procedureIndex'
    },
    {
        prepareAddPayload: (formData) => ({
            title: formData.title,
            description: formData.description,
            starts_at: '2025-05-05 12:00:00',
            ends_at: '2025-05-05 21:00:00',
            rooms: [
                {
                    roomId: 1,
                    starts_at: '2025-05-05 12:00:00',
                    ends_at: '2025-05-05 15:00:00',
                    description: 'test',
                },
                {
                    roomId: 3,
                    starts_at: '2025-05-05 15:00:00',
                    ends_at: '2025-05-05 18:00:00',
                    description: 'test',
                }
            ]
        }),
        prepareUpdatePayload: (formData, existingProcedure) => ({
            id: existingProcedure.id,
            title: formData.title,
            description: formData.description,
            starts_at: '2025-05-05 12:00:00',
            ends_at: '2025-05-05 15:00:00',
            rooms: [
                {
                    roomId: 1,
                    starts_at: '2025-05-05 12:00:00',
                    ends_at: '2025-05-05 15:00:00',
                    description: 'test',
                }
            ]
        })
    }
);
</script>
