<template>
    <AuthLayout show-footer>
        <Head title="Log in" />

        <h3 class="fw-semibold mb-2">Forgot Pasword</h3>

        <p class="text-muted mb-4">Wrong or expired token.</p>
        <p class="text-muted">Ο σύνδεσμος δημιουργίας νέου κωδικού πρόσβασης δεν είναι έγκυρος, έχει ήδη χρησιμοποιηθεί ή έχει λήξει. Δημιουργήστε ένα νέο αίτημα κωδικού πρόσβασης επαναφοράς, <a href="{{ route('forgotPassword') }}" class="link-primary fw-bold">πατώντας εδώ</a>.</p>


        <!--        <div class="d-flex justify-content-center mb-3 gap-2">
            <a href="#!" class="btn btn-soft-danger avatar-lg"><i class="ti ti-brand-google-filled fs-24"></i></a>
            <a href="#!" class="btn btn-soft-success avatar-lg"><i class="ti ti-brand-apple fs-24"></i></a>
            <a href="#!" class="btn btn-soft-primary avatar-lg"><i class="ti ti-brand-facebook fs-24"></i></a>
            <a href="#!" class="btn btn-soft-info avatar-lg"><i class="ti ti-brand-linkedin fs-24"></i></a>
        </div>
        <p class="fs-13 fw-semibold">Or Login With Email</p>-->


    </AuthLayout>
</template>

<script setup lang="ts">
import AuthLayout from '@/layouts/AuthLayout.vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';

const props = usePage().props;

defineProps<{
    status?: string;
    canResetPassword: boolean;
}>();

const error = ref('');

const form = useForm({
    email: '<EMAIL>',
});

const submit = () => {
    form.post('api/forgot-password', {
        onFinish: () => form.reset('email'),
    });
};
</script>
