<template>
    <AuthLayout show-footer>
        <Head title="Log in" />

        <h3 class="fw-semibold mb-2">Reset Pasword</h3>

        <h4 class="text-center mb-4">Δημιουργήστε νέο κωδικό πρόσβασης</h4>
        <p class="text-center">Για να δημιουργήσετε έναν νέο κωδικό πρόσβασης, πληκτρολογήστε το νέο κωδικό πρόσβασης και την επαλήθευση του και τέλος κάντε κλικ στο κουμπί "Δημιουργία νέου κωδικού πρόσβασης".</p>

        <strong>Panagioti</strong> tha kanei axios ajax call sto /api/reset-password. h sto /api/en/reset-password antistixa kai tha stelnei password, token, email.
        To password na einai idio me to password2 kai ta view password klp tha ta xirizete mono h vue.

        <!--        <div class="d-flex justify-content-center mb-3 gap-2">
            <a href="#!" class="btn btn-soft-danger avatar-lg"><i class="ti ti-brand-google-filled fs-24"></i></a>
            <a href="#!" class="btn btn-soft-success avatar-lg"><i class="ti ti-brand-apple fs-24"></i></a>
            <a href="#!" class="btn btn-soft-primary avatar-lg"><i class="ti ti-brand-facebook fs-24"></i></a>
            <a href="#!" class="btn btn-soft-info avatar-lg"><i class="ti ti-brand-linkedin fs-24"></i></a>
        </div>
        <p class="fs-13 fw-semibold">Or Login With Email</p>-->

        <p v-if="status" class="text-success mb-3">
            {{ status }}
        </p>

        <b-form @submit.prevent="submit" class="mb-3 text-start">
            <div v-if="error.length > 0" class="text-danger mb-2">{{ error }}</div>

            <b-form-group label="Email" class="mb-3">
                <b-form-input type="email" id="example-email" name="email" v-model="form.email" placeholder="Enter your email" />
                <p v-if="form.errors.email" class="text-danger">
                    {{ form.errors.email }}
                </p>
            </b-form-group>

            <div class="d-grid">
                <b-button variant="primary" type="submit" :disabled="form.processing">Login</b-button>
            </div>
        </b-form>

        <p class="text-danger fs-14 mb-4">
            Don't have an account? // todo
            <!--            <Link :href="route('register')" class="fw-semibold text-dark ms-1">Sign Up !</Link>-->
        </p>
    </AuthLayout>
</template>

<script setup lang="ts">
import AuthLayout from '@/layouts/AuthLayout.vue';
import { Head, Link, useForm, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';
import { BForm, BFormGroup } from 'bootstrap-vue-next';

const props = usePage().props;

defineProps<{
    status?: string;
    canResetPassword: boolean;
}>();

const error = ref('');

const form = useForm({
    email: '<EMAIL>',
});

const submit = () => {
    form.post('api/forgot-password', {
        onFinish: () => form.reset('email'),
    });
};
</script>
