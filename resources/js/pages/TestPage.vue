<template>
    <VerticalLayout>
        <PageTitle title="Test Consultation Steps" subtitle="Rooms" />
        <b-row>
            <b-col>
                <b-card no-body>
                    <b-card-body></b-card-body>

                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import Comments from '@/components/Comments.vue';
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { Icon } from '@iconify/vue';
import { BFormGroup, BFormInput } from 'bootstrap-vue-next';
import { ref, watch } from 'vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'

// Type definitions
// Single user structure, reused for both recipient and sender
interface User {
    name: string;
    image: string;
}

// A comment by "You" (uses 'recipient')
interface User {
    name: string;
    image: string;
}

interface Comment {
    recipient?: User;
    sender?: User;
    timestamp: string;
    messages: string[];
}

const props = defineProps<{
    comments: Comment[];
}>();

const date = ref('2025-08-29')

watch(date, (newVal) => {
    console.log('date', newVal);
});


</script>
