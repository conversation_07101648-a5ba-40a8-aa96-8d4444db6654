<template>
    <VerticalLayout>
        <PageTitle title="Test Room" subtitle="Rooms" />
        <b-row>
            <b-col xl="6" lg="12">
                <b-card no-body>
                    <b-card-body>
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <span class="badge bg-success-subtle text-success fs-13 rounded-pill px-2 py-1">New</span>
                            </div>
                        </div>
                        <div class="mt-3 mb-1">
                            <a href="#!" class="text-dark fs-20 fw-medium">Climate Action Plan 2030: Public Consultation</a>
                        </div>
                        <p class="text-muted fw-medium fs-14 mb-1"><span class="text-dark">Organizer : </span> Ministry of Environment</p>
                        <p class="text-muted fw-medium fs-14 mb-1"><span class="text-dark">Date : </span> 22 August 2025</p>
                        <p class="text-muted fw-medium fs-14 mb-1">
                            <span class="text-dark">Location :</span> City Hall Conference Room, Main Street
                        </p>
                        <h4 class="text-dark fw-medium">Category :</h4>
                        <p class="mb-1">Environmental Policy / Community Engagement</p>
                        <h4 class="text-dark fw-medium">Description :</h4>
                        <p class="mb-1">
                            A public forum organized to invite citizens, stakeholders, and experts to provide feedback and suggestions on proposed
                            strategies and actions for the city's 2030 climate and sustainability roadmap. The purpose is to ensure that community
                            voices are included in policy development and that the final plan reflects local needs and priorities.
                        </p>
                        <p class="mb-1">
                            A public forum organized to invite citizens, stakeholders, and experts to provide feedback and suggestions on proposed
                            strategies and actions for the city's 2030 climate and sustainability roadmap. The purpose is to ensure that community
                            voices are included in policy development and that the final plan reflects local needs and priorities.
                        </p>
                        <a href="#!" class="link-primary">Read More...</a>
                        <h4 class="text-dark fw-medium mt-3">Details :</h4>
                        <ul class="d-flex flex-column fs-14 mb-0 gap-1">
                            <li>
                                <strong>Objective:</strong> Gather public feedback on proposed climate actions, targets, and policies for the city's
                                2030 sustainability roadmap.
                            </li>
                            <li><strong>Format:</strong> Presentations, Q&A session, breakout discussions with stakeholders</li>
                            <li>
                                <strong>How to Participate:</strong> Register online by 15 August or attend in person; virtual participation available
                                via livestream
                            </li>
                            <li><strong>Contact:</strong> <EMAIL></li>
                        </ul>


                        <b-form-group label="Date" label-for="example-date" class="mb-3">
                            <h2>VueDatePicker</h2>
                            <VueDatePicker v-model="date"></VueDatePicker>
                        </b-form-group>

                        <h4 class="text-dark fw-medium mt-3 mb-2 pb-1">Additional Information :</h4>
                        <div class="rounded border border-dashed p-2 text-center">
                            <b-row>
                                <b-col lg="3" cols="4" class="border-end">
                                    <p class="text-muted fw-medium fs-14 mb-0"><span class="text-dark">Info 1 : </span> info text</p>
                                </b-col>
                                <b-col lg="3" cols="4" class="border-end">
                                    <p class="text-muted fw-medium fs-14 mb-0"><span class="text-dark">Info 2 : </span> info text</p>
                                </b-col>
                                <b-col lg="3" cols="4" class="border-end">
                                    <p class="text-muted fw-medium fs-14 mb-0"><span class="text-dark">Info 3 : </span> info text</p>
                                </b-col>
                                <b-col lg="3" cols="4" class="border-end">
                                    <p class="text-muted fw-medium fs-14 mb-0"><span class="text-dark">Info 4 : </span> info text</p>
                                </b-col>
                            </b-row>
                        </div>
                    </b-card-body>
                    <b-card-footer class="border-top border-dashed">
                        <b-row class="g-2">
                            <b-col lg="3">
                                <a href="#!" class="btn btn-primary d-flex align-items-center w-100 gap-1">
                                    <Icon icon="solar:cart-large-2-bold" class="fs-16 align-middle" />
                                    Button 1</a
                                >
                            </b-col>
                            <b-col lg="3">
                                <a href="#!" class="btn btn-success d-flex align-items-center w-100 gap-1">
                                    <Icon icon="solar:bag-check-bold" class="fs-16 align-middle" />
                                    Button 2</a
                                >
                            </b-col>
                            <b-col lg="3">
                                <a href="#!" class="btn btn-outline-danger d-flex align-items-center w-75 gap-1">
                                    <Icon icon="solar:heart-bold" class="fs-16 align-middle" />
                                    Button 3</a
                                >
                            </b-col>
                        </b-row>
                    </b-card-footer>
                </b-card>
            </b-col>
            <b-col xl="6" lg="12">
                <div class="card">
                    <div class="chat d-flex">
                        <Comments :comments="props.comments" />
                    </div>
                </div>
            </b-col>
            <b-col xl="12" lg="12">
                <b-card no-body>
                    <b-tabs content-class="m-3">
                        <b-tab title-item-class="p-2 pb-0">
                            <template v-slot:title>
                                <span class="d-block d-sm-none"><Icon icon="solar:notebook-bold" class="fs-20" /></span>
                                <span class="d-none d-sm-block"><Icon icon="solar:notebook-bold" class="fs-14 me-1 align-middle" /> other</span>
                            </template>
                        </b-tab>

                        <b-tab title-item-class="p-2 pb-0">
                            <template v-slot:title>
                                <span class="d-block d-sm-none"><Icon icon="solar:chat-dots-bold" class="fs-20" /></span>
                                <span class="d-none d-sm-block"><Icon icon="solar:chat-dots-bold" class="fs-14 me-1 align-middle" /> Other</span>
                            </template>
                            test
                        </b-tab>
                    </b-tabs>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import Comments from '@/components/Comments.vue';
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { Icon } from '@iconify/vue';
import { BFormGroup, BFormInput } from 'bootstrap-vue-next';
import { ref, watch } from 'vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'

// Type definitions
// Single user structure, reused for both recipient and sender
interface User {
    name: string;
    image: string;
}

// A comment by "You" (uses 'recipient')
interface User {
    name: string;
    image: string;
}

interface Comment {
    recipient?: User;
    sender?: User;
    timestamp: string;
    messages: string[];
}

const props = defineProps<{
    comments: Comment[];
}>();

const date = ref('2025-08-29')

watch(date, (newVal) => {
    console.log('date', newVal);
});


</script>
