<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-door" :title="t('rooms.title')" :subtitle="t('rooms.list')" subtitleRoute="roomIndex" />

        <b-row>
            <b-col cols="12">
                <b-card no-body>
                    <b-card-header class="border-bottom border-light">
                        <div class="d-flex justify-content-between flex-wrap gap-2">
                            <SearchInput :initialValue="searchInput" searchRoute="roomIndexPost" :placeholder="t('rooms.search')" />
                            <div>
                                <Link :href="route('roomAdd')" class="btn btn-primary"><i class="ti ti-plus me-1"></i>{{ t('rooms.add') }} </Link>
                            </div>
                        </div>
                    </b-card-header>

                    <b-table-simple responsive hover class="mb-0 text-nowrap">
                        <b-thead class="bg-light-subtle">
                            <b-tr>
                                <b-th class="ps-3" style="width: 50px">
                                    <b-form-checkbox :checked="allSelected" @change="toggleSelectAll" />
                                </b-th>
                                <b-th>ID</b-th>
                                <b-th><i class="ti ti-article fs-16 me-1 align-text-top" />{{ t('common.title') }}</b-th>
                                <b-th><i class="ti ti-user fs-16 me-1 align-text-top" />{{ t('common.admin') }}</b-th>
                                <b-th><i class="ti ti-users fs-16 me-1 align-text-top" />{{ t('users.title') }}</b-th>
                                <b-th><i class="ti ti-tags fs-16 me-1 align-text-top" />{{ t('tags.title') }}</b-th>
                                <b-th class="text-center" style="width: 120px"
                                    ><i class="ti ti-bolt fs-16 me-1 align-text-top" />{{ t('common.actions') }}</b-th
                                >
                            </b-tr>
                        </b-thead>
                        <b-tbody>
                            <b-tr v-for="room in rooms.data" :key="`room_${room.id}`">
                                <b-td class="ps-3">
                                    <b-form-checkbox :value="room.id" v-model="selectedRows" />
                                </b-td>
                                <b-td>{{ room.id }}</b-td>
                                <b-td>
                                    <Link variant="info" v-b-tooltip.hover.top="t('rooms.edit')" :href="route('updateRoom', { id: room.id })">{{
                                        room.title
                                    }}</Link>
                                </b-td>
                                <b-td>{{ room.admin.name }}</b-td>
                                <b-td>{{ room.users.map((user) => user.name).join(', ') }}</b-td>
                                <b-td>{{ room.tags.map((tag) => tag.title).join(', ') }}</b-td>

                                <b-td class="pe-3">
                                    <div class="hstack justify-content-end gap-1">
                                        <Link :href="route('updateRoom', { id: room.id })" class="btn btn-soft-success btn-icon btn-sm rounded-circle"
                                            ><i class="ti ti-edit fs-16"></i
                                        ></Link>

                                        <button @click="deleteWithConfirm(room.id)" class="btn btn-soft-danger btn-icon btn-sm rounded-circle">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>
                                </b-td>
                            </b-tr>
                        </b-tbody>
                    </b-table-simple>

                    <b-card-footer>
                        <div class="">
                            <button
                                v-if="selectedRows.length"
                                v-b-tooltip.hover.top="`TODO:${t('common.bulkDelete')}`"
                                @click="deleteMultipleWithConfirm"
                                class="btn btn-soft-danger btn-icon btn-sm rounded-circle"
                            >
                                <i class="ti ti-trash"></i>
                            </button>
                        </div>
                        <Pagination
                            :items="props.rooms"
                        />
                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { useDeleteActions } from '@/composables/useDeleteActions';
import { Room } from '@/types/room';
import { Link, usePage } from '@inertiajs/vue3';
import {
    BCard, BCardFooter,
    BCardHeader,
    BCol,
    BFormCheckbox,
    BRow,
    BTableSimple,
    BTbody, BTd,
    BTh,
    BThead,
    BTr,
    vBTooltip
} from 'bootstrap-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import Pagination from '@/components/Pagination.vue';
import SearchInput from '@/components/SearchInput.vue';

const { t } = useI18n();

// Define props passed from Laravel
const props = defineProps<{
    searchKey?: string;
    rooms: {
        data: Room[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();

// Delete Actions Composable
const {
    selectedRows,
    allSelected,
    toggleSelectAll,
    deleteWithConfirm,
    deleteMultipleWithConfirm
} = useDeleteActions(
    () => props.rooms.data,
    {
        entityName: 'room',
        deleteRoute: 'deleteRoom',
        massDeleteRoute: 'massDeleteRooms',
        successMessage: 'alerts.rooms.delete.success',
        errorMessage: 'alerts.rooms.delete.error',
        bulkSuccessMessage: 'alerts.rooms.delete.success',
        bulkErrorMessage: 'alerts.rooms.delete.error',
        bulkDeleteTranslationKey: 'rooms.bulkDelete'
    }
);

// Search Input
const searchInput = ref(usePage().props.searchKey as string || '');

</script>
