<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-door" :title="editMode ? t('rooms.edit') : t('rooms.add')" :subtitle="t('rooms.list')" subtitleRoute="roomIndex" />

        <b-card no-body>
            <b-card-body>
                <b-row>
                    <b-col xl="6" lg="12">
                        <b-form-group label-for="title" class="mb-3">
                            <template #label> <i class="ti ti-article fs-16 me-1 align-text-top" />{{ t('common.title') }} </template>
                            <b-form-input v-model="form.title" type="text" name="title" :placeholder="t('rooms.editPlaceholder')" />
                        </b-form-group>
                        <b-form-group label-for="adminId" class="mb-3">
                            <template #label> <i class="ti ti-user fs-16 me-1 align-text-top" />{{ t('common.admin') }} </template>
                            <ChoicesSelect
                                id="adminId"
                                v-model="form.adminId"
                                :options="[{ value: null, text: 'Select an admin' }, ...usersLookup]"
                                class="form-control"
                            />
                        </b-form-group>
                    </b-col>
                    <b-col xl="6" lg="12">
                        <b-form-group label-for="tagsLookup" class="mb-3">
                            <template #label> <i class="ti ti-tags fs-16 me-1 align-text-top" />{{ t('tags.title') }} </template>
                            <ChoicesSelect
                                id="tagsLookup"
                                v-model="form.tags"
                                :options="tagsLookup"
                                :choice-options="{ addItems: false, removeItemButton: true, duplicateItemsAllowed: false, paste: false }"
                                :multiple="true"
                            />
                        </b-form-group>

                        <b-form-group label-for="usersLookup" class="mb-3">
                            <template #label> <i class="ti ti-users fs-16 me-1 align-text-top" />{{ t('users.title') }} </template>
                            <ChoicesSelect
                                id="usersLookup"
                                v-model="form.users"
                                :options="usersLookup"
                                :choice-options="{ addItems: false, removeItemButton: true, duplicateItemsAllowed: false, paste: false }"
                                :multiple="true"
                            />
                        </b-form-group>
                    </b-col>
                </b-row>
                <b-form-group :label="t('common.description')" label-for="description" class="mb-3">
                    <template #label> <i class="ti ti-file-description fs-16 me-1 align-text-top" />{{ t('common.description') }} </template>
                    <Editor v-model="form.description" />
                </b-form-group>
                <div class="d-flex justify-content-between flex-wrap gap-2 mt-3">
                    <Link :href="route('roomIndex')" class="btn btn-secondary"> <i class="ti ti-arrow-left me-1"></i>{{ t('common.backToList') }}</Link>
                    <b-button variant="primary" @click="handleSubmit" type="submit" :disabled="isSubmitting">
                        <b-spinner v-if="isSubmitting" small class="me-1" />
                        <i v-else class="ti ti-device-floppy me-1"></i>
                        {{ editMode ? t('common.update') : t('common.save') }}
                    </b-button>
                </div>
            </b-card-body>
        </b-card>
        <b-card no-body>

        </b-card>
    </VerticalLayout>
</template>

<script setup lang="ts">
import ChoicesSelect from '@/components/ChoicesSelect.vue';
import Editor from '@/components/Editor.vue';
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { useFormActions } from '@/composables/useFormActions';
import { Room, RoomFormData } from '@/types/room';
import { Tag } from '@/types/tag';
import { User } from '@/types/user';
import { Link } from '@inertiajs/vue3';
import { BButton, BCard, BCardBody, BCol, BFormGroup, BRow, BSpinner } from 'bootstrap-vue-next';
import { computed, PropType, reactive } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Props
const props = defineProps({
    room: {
        type: Object as PropType<Room>,
        required: false,
    },
    users: {
        type: Array as PropType<User[]>,
        required: true,
    },
    tags: {
        type: Array as PropType<Tag[]>,
        required: true,
    },
});

const tagsLookup = computed(() =>
    props.tags.map((tag) => ({
        value: tag.id,
        text: tag.title,
    })),
);

const usersLookup = computed(() =>
    props.users.map((user) => ({
        value: user.id,
        text: user.name,
    })),
);

// Initialize form data with existing room data
const form = reactive<RoomFormData>({
    title: props.room?.title ?? '',
    description: props.room?.description ?? '',
    adminId: props.room?.adminId ?? null,
    tags: props.room?.tags.map((tag) => tag.id) ?? [],
    users: props.room?.users.map((user) => user.id) ?? [],
});

// Use the form actions composable
const {
    editMode,
    isSubmitting,
    handleSubmit
} = useFormActions(
    () => form,
    props.room,
    {
        entityName: 'room',
        addRoute: 'addRoomPost',
        updateRoute: 'updateRoomPost',
        addSuccessMessage: 'alerts.rooms.add.success',
        updateSuccessMessage: 'alerts.rooms.update.success',
        indexRoute: 'roomIndex'
    },
    {
        prepareAddPayload: (formData) => ({
            title: formData.title,
            description: formData.description,
            adminId: formData.adminId,
            tagIds: formData.tags,
            userIds: formData.users,
        }),
        prepareUpdatePayload: (formData, existingRoom) => ({
            id: existingRoom.id,
            title: formData.title,
            description: formData.description,
            adminId: formData.adminId,
            tagIds: formData.tags,
            userIds: formData.users,
        })
    }
);
</script>
