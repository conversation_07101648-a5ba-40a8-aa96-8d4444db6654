<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-users" :title="t('users.title')" :subtitle="t('users.list')" subtitleRoute="userIndex" />

        <b-row>
            <b-col cols="12">
                <b-card no-body>
                    <b-card-header class="border-bottom border-light">
                        <div class="d-flex justify-content-between flex-wrap gap-2">
                            <SearchInput :initialValue="searchInput" searchRoute="userIndexPost" :placeholder="t('users.search')" />
                            <div>
                                USER ADD BUTTON
<!--                                <Link :href="route('userAdd')" class="btn btn-primary"><i class="ti ti-plus me-1"></i>{{ t('users.add') }} </Link>-->
                            </div>
                        </div>
                    </b-card-header>

                    <b-table-simple responsive hover class="mb-0 text-nowrap">
                        <b-thead class="bg-light-subtle">
                            <b-tr>
                                <b-th class="ps-3" style="width: 50px">
                                    <b-form-checkbox :checked="allSelected" @change="toggleSelectAll" />
                                </b-th>
                                <b-th>ID</b-th>
                                <b-th><i class="ti ti-article fs-16 me-1 align-text-top" />{{ t('common.title') }}</b-th>
                                <b-th class="text-center" style="width: 120px"
                                    ><i class="ti ti-bolt fs-16 me-1 align-text-top" />{{ t('common.actions') }}</b-th
                                >
                            </b-tr>
                        </b-thead>
                        <b-tbody>
                            <b-tr v-for="user in users.data" :key="`user_${user.id}`">
                                <b-td class="ps-3">
                                    <b-form-checkbox :value="user.id" v-model="selectedRows" />
                                </b-td>
                                <b-td>{{ user.id }}</b-td>
                                <b-td>
                                    {{ user.name }} - link here
<!--                                    <Link variant="info" v-b-tooltip.hover.top="t('users.edit')" :href="route('updateUser', { id: user.id })">{{
                                        user.name
                                    }}</Link>-->
                                </b-td>

                                <b-td class="pe-3">
<!--                                    <div class="hstack justify-content-end gap-1">
                                        <Link :href="route('updateUser', { id: user.id })" class="btn btn-soft-success btn-icon btn-sm rounded-circle"
                                            ><i class="ti ti-edit fs-16"></i
                                        ></Link>

                                        <button @click="deleteWithConfirm(user.id)" class="btn btn-soft-danger btn-icon btn-sm rounded-circle">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>-->
                                </b-td>
                            </b-tr>
                        </b-tbody>
                    </b-table-simple>

                    <b-card-footer>
                        <div class="">
                            <button
                                v-if="selectedRows.length"
                                v-b-tooltip.hover.top="`TODO:${t('common.bulkDelete')}`"
                                @click="deleteMultipleWithConfirm"
                                class="btn btn-soft-danger btn-icon btn-sm rounded-circle"
                            >
                                <i class="ti ti-trash"></i>
                            </button>
                        </div>
                        <Pagination
                            :items="props.users"
                        />
                        {{ roles }}
                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { useDeleteActions } from '@/composables/useDeleteActions';
import { User } from '@/types/user';
import { Link, usePage } from '@inertiajs/vue3';
import {
    BCard, BCardFooter,
    BCardHeader,
    BCol,
    BFormCheckbox,
    BRow,
    BTableSimple,
    BTbody, BTd,
    BTh,
    BThead,
    BTr,
    vBTooltip
} from 'bootstrap-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import Pagination from '@/components/Pagination.vue';
import SearchInput from '@/components/SearchInput.vue';

const { t } = useI18n();

// Define props passed from Laravel
const props = defineProps<{
    roles: any;
    searchKey?: string;
    users: {
        data: User[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();

// Delete Actions Composable
const {
    selectedRows,
    allSelected,
    toggleSelectAll,
    deleteWithConfirm,
    deleteMultipleWithConfirm
} = useDeleteActions(
    () => props.users.data,
    {
        entityName: 'user',
        deleteRoute: 'deleteUser',
        massDeleteRoute: 'massDeleteUsers',
        successMessage: 'alerts.users.delete.success',
        errorMessage: 'alerts.users.delete.error',
        bulkSuccessMessage: 'alerts.users.delete.successBulk',
        bulkErrorMessage: 'alerts.users.delete.errorBulk',
        bulkDeleteTranslationKey: 'users.bulkDelete'
    }
);

// Search Input
const searchInput = ref(usePage().props.searchKey as string || '');

</script>
