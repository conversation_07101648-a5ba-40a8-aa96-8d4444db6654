import type { NotificationType } from '@/layouts/partials/types';
import type { MegaMenuItemType, MenuItemType } from '@/types/layout';

import deFlag from '@/images/flags/de.svg';
import esFlag from '@/images/flags/es.svg';
import inFlag from '@/images/flags/in.svg';
import itFlag from '@/images/flags/it.svg';
import ruFlag from '@/images/flags/ru.svg';
import usFlag from '@/images/flags/us.svg';

import avatar10 from '@/images/users/avatar-10.jpg';
import avatar2 from '@/images/users/avatar-2.jpg';
import avatar4 from '@/images/users/avatar-4.jpg';
import avatar7 from '@/images/users/avatar-7.jpg';

// topbar
export const megaMenuItems: MegaMenuItemType[] = [
    {
        title: 'UI Components',
        links: [
            {
                label: 'Widgets',
                url: '',
            },
            {
                label: 'Dragula',
                url: '',
            },
            {
                label: 'Dropdowns',
                url: '',
            },
            {
                label: 'Ratings',
                url: '',
            },
            {
                label: 'Sweet Alerts',
                url: '',
            },
            {
                label: 'Scrollbar',
                url: '',
            },
            {
                label: 'Range Slider',
                url: '',
            },
        ],
    },
    {
        title: 'Applications',
        links: [
            {
                label: 'eCommerce Pages',
                url: '',
            },
            {
                label: 'Hospital',
                url: '',
            },
            {
                label: 'Email',
                url: '',
            },
            {
                label: 'Calendar',
                url: '',
            },
            {
                label: 'Kanban Board',
                url: '',
            },
            {
                label: 'Invoice Management',
                url: '',
            },
            {
                label: 'Pricing',
                url: '',
            },
        ],
    },
    {
        title: 'Extra Pages',
        bgVariant: 'light',
        links: [
            {
                label: 'Left Sidebar with User',
                url: '',
            },
            {
                label: 'Menu Collapsed',
                url: '',
            },
            {
                label: 'Small Left Sidebar',
                url: '',
            },
            {
                label: 'New Header Style',
                url: '',
            },
            {
                label: 'My Account',
                url: '',
            },
            {
                label: 'Maintenance & Coming Soon',
                url: '',
            },
        ],
    },
];

export const languages: MenuItemType[] = [
    {
        image: usFlag,
        label: 'English',
    },
    {
        image: inFlag,
        label: 'Hindi',
    },
    {
        image: deFlag,
        label: 'German',
    },
    {
        image: itFlag,
        label: 'Italian',
    },
    {
        image: esFlag,
        label: 'Spanish',
    },
    {
        image: ruFlag,
        label: 'Russian',
    },
];
export const notifications: NotificationType[] = [
    {
        sender: {
            image: avatar2,
            name: 'Glady Haid',
        },
        message: 'Glady Haid commented on paces admin status.',
        timestamp: '25m ago',
        type: 'commented',
    },
    {
        sender: {
            image: avatar4,
            name: 'Tommy Berry',
        },
        message: 'Tommy Berry donated $100.00 for Carbon removal program',
        timestamp: '58m ago',
        type: 'donated',
    },
    {
        message: 'You withdraw a $500 by New York ATM',
        timestamp: '2h ago',
        type: 'other',
    },
    {
        sender: {
            image: avatar7,
            name: 'Richard Allen',
        },
        message: 'Richard Allen followed you in Facebook',
        timestamp: '3h ago',
        type: 'followed',
    },
    {
        sender: {
            image: avatar10,
            name: 'Victor Collier',
        },
        message: 'Victor Collier liked you recent photo in Instagram',
        timestamp: '10h ago',
        type: 'liked',
    },
];

export const profileMenuItems: MenuItemType[] = [
    {
        label: 'My Account',
        icon: 'ti ti-user-hexagon',
    },
    {
        label: 'Sign Out',
        icon: 'ti ti-logout',
        url: 'logoff',
    },
];

// footer
export const footerItems: MenuItemType[] = [
    {
        label: 'Support',
    },
    {
        label: 'Contact Us',
    },
];
