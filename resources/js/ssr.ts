import { createInertiaApp } from '@inertiajs/vue3';
import createServer from '@inertiajs/vue3/server';
import { renderToString } from '@vue/server-renderer';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createSSRApp, h } from 'vue';
import { route as ziggyRoute, Config } from 'ziggy-js';
import type { DefineComponent } from 'vue';

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

interface PageProps {
    ziggy?: Config & {
        location: string;
    };
    [key: string]: any;
}

createServer((page) =>
    createInertiaApp({
        page,
        render: renderToString,
        title: (title) => `${title} - ${appName}`,
        resolve: (name) => resolvePageComponent(`./pages/${name}.vue`, {} as any),
        setup({ App, props, plugin }) {
            const app = createSSRApp({ render: () => h(App, props) });

            // Type the page props
            const pageProps = page.props as PageProps;

            // Configure Ziggy for SSR...
            const ziggyConfig: Config = pageProps.ziggy ? {
                ...pageProps.ziggy,
                location: new URL(pageProps.ziggy.location),
            } : {} as Config;

            // Create route function...
            const route = ((name?: string, params?: any, absolute?: boolean) => {
                if (!name) {
                    return ziggyRoute(undefined, undefined, absolute, ziggyConfig);
                }
                return ziggyRoute(name, params, absolute, ziggyConfig);
            }) as typeof ziggyRoute;

            // Make route function available globally...
            app.config.globalProperties.route = route;

            // Make route function available globally for SSR...
            if (typeof window === 'undefined') {
                (globalThis as any).route = route;
            }

            app.use(plugin);

            return app;
        },
    }),
);
