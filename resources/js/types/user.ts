export interface User {
    id: number;
    name: string;
    email: string;
    registered_at: string;
}

export interface UsersPageProps {
    users: {
        data: User[];
        links: { url: string | null; label: string; active: boolean }[];
        meta: {
            current_page: number;
            last_page: number;
            per_page: number;
            total: number;
            // etc.
        };
    };
}
