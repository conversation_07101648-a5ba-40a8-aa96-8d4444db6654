<?php

use App\Http\Controllers\AuthController;
use App\Http\Middleware\LanguageMiddleware;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;


$language = config('languages.languages')[0]['code']; // default

foreach (config('languages.languages') as $lang) {
    if((request()->segment(1) ?? '') === $lang['code']) {
        App::setLocale($lang['code']);
    }
}

Route::middleware([
    LanguageMiddleware::class,
])
    ->group(
        function () {
            Route::post(trans('routes.forgotPassword.furl'), [AuthController::class, 'forgotPasswordPost'])
                ->name('forgotPasswordPost');
            Route::post(trans('routes.resetPassword.furl'), [AuthController::class, 'resetPasswordPost'])
                ->name('resetPasswordPost');
        }
    );

