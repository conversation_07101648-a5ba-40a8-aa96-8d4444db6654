<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProcedureController;
use App\Http\Controllers\RoomController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\UserController;
use App\Http\Middleware\CheckUserActive;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\LanguageMiddleware;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;

$language = config('languages.languages')[0]['code']; // default

foreach (config('languages.languages') as $lang) {
    if((request()->segment(1) ?? '') === $lang['code']) {
        App::setLocale($lang['code']);
    }
}

Route::middleware([
    LanguageMiddleware::class,
    HandleInertiaRequests::class
])
    ->group(
        function () {
            Route::get('testpage', [TestController::class, 'testpage'])
                ->name('testpage');

            // USER
            Route::get(trans('routes.login.furl'), [AuthController::class, 'login'])
                ->name('login');
            Route::post(trans('routes.login.furl'), [AuthController::class, 'loginPost'])
                ->name('loginPost');
            Route::get(trans('routes.register.furl'), [UserController::class, 'register'])
                ->name('register');
            Route::post(trans('routes.register.furl'), [UserController::class, 'registerPost'])
                ->name('registerPost');
            Route::get(trans('routes.pendingConfirmation.furl'), [AuthController::class, 'pendingConfirmation'])
                ->name('pendingConfirmation');



            // FORGOT PASSWORD
            Route::get(trans('routes.forgotPassword.furl'), [AuthController::class, 'forgotPassword'])
                ->name('forgotPassword');
            Route::get(trans('routes.resetPassword.furl'), [AuthController::class, 'resetPassword'])
                ->name('resetPassword');
        }
    );

Route::middleware([
    'auth',
    CheckUserActive::class,
    LanguageMiddleware::class,
    HandleInertiaRequests::class
])
    ->group(
        function () {
            Route::get(trans('routes.dashboard.furl'), [DashboardController::class, 'dashboard'])
                ->name('dashboard');

            // TAGS
            Route::get(trans('routes.tagIndex.furl'), [TagController::class, 'index'])
                ->name('tagIndex');
            Route::post(trans('routes.tagIndex.furl'), [TagController::class, 'indexPost'])
                ->name('tagIndexPost');
            Route::get(trans('routes.tagAdd.furl'), [TagController::class, 'addTag'])
                ->name('tagAdd');
            Route::post(trans('routes.tagAdd.furl'), [TagController::class, 'addTagPost'])
                ->name('addTagPost');
            Route::delete(trans('routes.tagDelete.furl'), [TagController::class, 'deleteTag'])
                ->where('id', '[0-9]+')
                ->name('deleteTag');
            Route::delete(trans('routes.massDeleteTags.furl'), [TagController::class, 'massDeleteTags'])->name('massDeleteTags');
            Route::get(trans('routes.tagUpdate.furl'), [TagController::class, 'updateTag'])
                ->where('id', '[0-9]+')
                ->name('updateTag');
            Route::post(trans('routes.tagUpdate.furl'), [TagController::class, 'updateTagPost'])
                ->where('id', '[0-9]+')
                ->name('updateTagPost');


            // ROOMS
            Route::get(trans('routes.roomIndex.furl'), [RoomController::class, 'index'])
                ->name('roomIndex');
            Route::post(trans('routes.roomIndex.furl'), [RoomController::class, 'indexPost'])
                ->name('roomIndexPost');
            Route::get(trans('routes.roomAdd.furl'), [RoomController::class, 'addRoom'])
                ->name('roomAdd');
            Route::post(trans('routes.roomAdd.furl'), [RoomController::class, 'addRoomPost'])
                ->name('addRoomPost');
            Route::delete('/rooms/{id}', [RoomController::class, 'deleteRoom'])
                ->where('id', '[0-9]+')
                ->name('deleteRoom');
            Route::get(trans('routes.roomUpdate.furl'), [RoomController::class, 'updateRoom'])
                ->where('id', '[0-9]+')
                ->name('updateRoom');
            Route::post(trans('routes.roomUpdate.furl'), [RoomController::class, 'updateRoomPost'])
                ->where('id', '[0-9]+')
                ->name('updateRoomPost');


            Route::get(trans('routes.userIndex.furl'), [UserController::class, 'index'])
                ->name('userIndex');
            Route::post(trans('routes.userIndex.furl'), [UserController::class, 'indexPost'])
                ->name('userIndexPost');

            // AUTH
            Route::get(trans('routes.logoff.furl'), [AuthController::class, 'logoff'])
                ->name('logoff');

            // PROCEDURES
            Route::get(trans('routes.procedureIndex.furl'), [ProcedureController::class, 'index'])
                ->name('procedureIndex');
            Route::post(trans('routes.procedureIndex.furl'), [ProcedureController::class, 'indexPost'])
                ->name('procedureIndexPost');
            Route::get(trans('routes.procedureAdd.furl'), [ProcedureController::class, 'addProcedure'])
                ->name('procedureAdd');
            Route::post(trans('routes.procedureAdd.furl'), [ProcedureController::class, 'addProcedurePost'])
                ->name('addProcedurePost');
            Route::delete(trans('routes.procedureDelete.furl'), [ProcedureController::class, 'deleteProcedure'])
                ->where('id', '[0-9]+')
                ->name('deleteProcedure');
            Route::delete(trans('routes.massDeleteProcedures.furl'), [ProcedureController::class, 'massDeleteProcedures'])
                ->name('massDeleteProcedures');
            Route::get(trans('routes.procedureUpdate.furl'), [ProcedureController::class, 'updateProcedure'])
                ->where('id', '[0-9]+')
                ->name('updateProcedure');
            Route::post(trans('routes.procedureUpdate.furl'), [ProcedureController::class, 'updateProcedurePost'])
                ->where('id', '[0-9]+')
                ->name('updateProcedurePost');
        }
    );
