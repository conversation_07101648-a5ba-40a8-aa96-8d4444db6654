/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BBadge: typeof import('bootstrap-vue-next/components/BBadge')['BBadge']
    BButton: typeof import('bootstrap-vue-next/components/BButton')['BButton']
    BCard: typeof import('bootstrap-vue-next/components/BCard')['BCard']
    BCardBody: typeof import('bootstrap-vue-next/components/BCard')['BCardBody']
    BCardFooter: typeof import('bootstrap-vue-next/components/BCard')['BCardFooter']
    BCardHeader: typeof import('bootstrap-vue-next/components/BCard')['BCardHeader']
    BCardTitle: typeof import('bootstrap-vue-next/components/BCard')['BCardTitle']
    BCol: typeof import('bootstrap-vue-next/components/BContainer')['BCol']
    BCollapse: typeof import('bootstrap-vue-next/components/BCollapse')['BCollapse']
    BDropdown: typeof import('bootstrap-vue-next/components/BDropdown')['BDropdown']
    BDropdownItem: typeof import('bootstrap-vue-next/components/BDropdown')['BDropdownItem']
    BForm: typeof import('bootstrap-vue-next/components/BForm')['BForm']
    BFormCheckbox: typeof import('bootstrap-vue-next/components/BFormCheckbox')['BFormCheckbox']
    BFormGroup: typeof import('bootstrap-vue-next/components/BFormGroup')['BFormGroup']
    BFormInput: typeof import('bootstrap-vue-next/components/BFormInput')['BFormInput']
    BFormSelect: typeof import('bootstrap-vue-next/components/BFormSelect')['BFormSelect']
    BFormTextarea: typeof import('bootstrap-vue-next/components/BFormTextarea')['BFormTextarea']
    BModal: typeof import('bootstrap-vue-next/components/BModal')['BModal']
    BOffcanvas: typeof import('bootstrap-vue-next/components/BOffcanvas')['BOffcanvas']
    BPagination: typeof import('bootstrap-vue-next/components/BPagination')['BPagination']
    BRow: typeof import('bootstrap-vue-next/components/BContainer')['BRow']
    BTab: typeof import('bootstrap-vue-next/components/BTabs')['BTab']
    BTableSimple: typeof import('bootstrap-vue-next/components/BTable')['BTableSimple']
    BTabs: typeof import('bootstrap-vue-next/components/BTabs')['BTabs']
    BTbody: typeof import('bootstrap-vue-next/components/BTable')['BTbody']
    BTd: typeof import('bootstrap-vue-next/components/BTable')['BTd']
    BTh: typeof import('bootstrap-vue-next/components/BTable')['BTh']
    BThead: typeof import('bootstrap-vue-next/components/BTable')['BThead']
    BTr: typeof import('bootstrap-vue-next/components/BTable')['BTr']
  }
  export interface GlobalDirectives {
    vBToggle: typeof import('bootstrap-vue-next/directives/BToggle')['vBToggle']
    vBTooltip: typeof import('bootstrap-vue-next/directives/BTooltip')['vBTooltip']
  }
}
