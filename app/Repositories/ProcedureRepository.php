<?php

namespace App\Repositories;

use App\DTO\Requests\ProcedureRequestDTO;
use App\Models\Procedure;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ProcedureRepository
{
    public function getProcedures(ProcedureRequestDTO $procedureRequestDTO): LengthAwarePaginator|Collection
    {
        $now = now();
        $query = Procedure::query();

        if ($procedureRequestDTO->id) {
            $query->where('id', $procedureRequestDTO->id);
        }

        if (count($procedureRequestDTO->with)) {
            $query->with($procedureRequestDTO->with);
        }

        if ($procedureRequestDTO->searchKey) {
            $query->where(function ($q) use ($procedureRequestDTO) {
                $q->where('title', 'like', '%'.$procedureRequestDTO->searchKey.'%')
                    ->orWhere('description', 'like', '%'.$procedureRequestDTO->searchKey.'%');
            });
        }

        if ($procedureRequestDTO->activePeriod === true) {
            $query->where('starts_at', '<=', $now)
                ->where('ends_at', '>=', $now);
        }

        if ($procedureRequestDTO->activePeriod === false) {
            $query->where(function ($q) use ($now) {
                $q->where('ends_at', '<', $now)      // already finished
                ->orWhere('starts_at', '>', $now); // not started yet
            });
        }

        $query->{$procedureRequestDTO->order === 'asc' ? 'orderBy' : 'orderByDesc'}($procedureRequestDTO->orderBy);

        return $query->paginate($procedureRequestDTO->rpp);
    }

    public function deleteProcedure(int $procedureId): void
    {
        Procedure::find($procedureId)
            ->update(['isDeleted' => 1]);
    }

    public function upsert(array $data): Procedure
    {
        if ($data['id'] ?? null) {
            $procedure = Procedure::find($data['id']);
        } else {
            $procedure = new Procedure();
        }

        $procedure->fill($data);
        $procedure->save();

        $procedure->procedureRooms()
            ->sync($data['rooms']);

        return $procedure;
    }

    public function massDelete(array $ids): void
    {
        Procedure::whereIn('id', $ids)
            ->update(['isDeleted' => 1]);
    }
}
