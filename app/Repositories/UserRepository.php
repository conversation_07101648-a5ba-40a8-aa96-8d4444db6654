<?php

namespace App\Repositories;

use App\DTO\Requests\TagRequestDTO;
use App\DTO\Requests\UserRequestDTO;
use App\Mail\GenericMail;
use App\Models\PasswordReset;
use App\Models\User;
use App\Services\TagService;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;

class UserRepository
{
    public function getUsers(UserRequestDTO $userRequestDTO): LengthAwarePaginator|Collection
    {
        $query = User::query();

        if ($userRequestDTO->isActive === true) {
            $query->where('isActive', 1);
        }

        if ($userRequestDTO->roles) {
            $query->whereHas('roles', function ($query) use ($userRequestDTO) {
                $query->whereIn('name', $userRequestDTO->roles);
            });
        }

        if ($userRequestDTO->with) {
            $query->with($userRequestDTO->with);
        }

        if ($userRequestDTO->status === 'active') {
            $query->where('isActive', 1);
        }

        if ($userRequestDTO->email) {
            $query->where('email', $userRequestDTO->email);
        }

        if ($userRequestDTO->id) {
            $query->where('id', $userRequestDTO->id);
        }

        if ($userRequestDTO->searchKey) {
            $query->where(function ($query) use ($userRequestDTO) {
                $query->where('name', 'like', '%'.$userRequestDTO->searchKey.'%')
                    ->orWhere('email', 'like', '%'.$userRequestDTO->searchKey.'%')
                    ->orWhere('surname', 'like', '%'.$userRequestDTO->searchKey.'%');
//                    ->orWhere('telephone', 'like', '%'.$userRequestDTO->searchKey.'%');
            });
        }

        if (count($userRequestDTO->orderBy)) {
            foreach ($userRequestDTO->orderBy as $orderBy) {
                $query->orderBy($orderBy);
            }
        }

        if ($userRequestDTO->all === true) {
            return $query->get();
        }

        return $query->paginate($userRequestDTO->rpp);
    }

    public function requestForgotPassword(User $user): string
    {
        return $token = Password::createToken($user);
    }

    public function getPasswordReset(string $token, string $email):bool
    {
        $record = DB::table('password_reset_tokens')
            ->where('email', $email)
            ->first();

        return $record &&
               Hash::check($token, $record->token) &&
               Carbon::parse($record->created_at)
                   ->addMinutes(config('auth.passwords.users.expire'))
                   ->isFuture();
    }

    public function resetUserPassword(array $data): string
    {
        return Password::reset(
           $data,
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                ])->save();
            }
        );
    }

    public function upsert(array $data): User
    {
        if($data['id'] ?? null) {
            $user = User::find($data['id']);
            $user->fill($data);
            $user->save();
            $user->tags()->sync($data['tagIds']);
        } else {
            $user = new User();
            $data['password']= Hash::make($data['password']);
            $user->fill($data);
            $user->save();

            $tagRequestDTO = new TagRequestDTO();
            $tagRequestDTO->searchKey = 'New User';
            $tagRequestDTO->orderBy = 'id';
            $tagRequestDTO->all = true;
            $user->tags()->attach((new TagService())->getTags($tagRequestDTO)[0]);
        }
        return $user;
    }
}
