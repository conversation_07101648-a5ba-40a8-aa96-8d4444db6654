<?php

namespace App\Repositories;

use App\DTO\Requests\TagRequestDTO;
use App\Models\Tag;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class TagRepository
{
    public function getTags(TagRequestDTO $tagRequestDTO): LengthAwarePaginator|Collection
    {
        $query = Tag::query();

        if($tagRequestDTO->id) {
            $query->where('id', $tagRequestDTO->id);
        }

        if ($tagRequestDTO->searchKey) {
            $query->where('title', 'like', '%'.$tagRequestDTO->searchKey.'%');
        }

        $query->{$tagRequestDTO->order === 'asc' ? 'orderBy' : 'orderByDesc'}($tagRequestDTO->orderBy);

        if($tagRequestDTO->all) {
            return $query->get();
        }

        return $query->paginate($tagRequestDTO->rpp);
    }

    public function deleteTag(int $tagId): void
    {
        Tag::find($tagId)->delete();
    }

    public function upsert(array $data): Tag
    {
        if($data['id'] ?? null) {
            $tag = Tag::find($data['id']);
        } else {
            $tag = new Tag();
        }

        $tag->fill($data);
        $tag->save();

        return $tag;
    }

    public function massDelete(array $ids): void
    {
        Tag::whereIn('id', $ids)->where('system', 0)->delete();
    }

}
