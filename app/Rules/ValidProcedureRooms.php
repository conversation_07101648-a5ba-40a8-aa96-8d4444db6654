<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Carbon;

class ValidProcedureRooms implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function __construct(
        protected string|\DateTimeInterface $procedureStartsAt,
        protected string|\DateTimeInterface $procedureEndsAt,
    ) {
    }

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($value === null) {
            return;
        }            // rooms is nullable
        if (!is_array($value)) {
            $fail('The :attribute must be an array.');

            return;
        }

        // Parse the procedure window
        try {
            $procStart = Carbon::parse($this->procedureStartsAt);
            $procEnd = Carbon::parse($this->procedureEndsAt);
        } catch (\Throwable) {
            return;
        }        // other rules will flag bad dates
        if ($procEnd->lt($procStart)) {
            return;
        }   // let base rule report this

        $seenRoomIds = [];
        $intervals = []; // only push valid intervals so overlap checks aren’t noisy

        foreach ($value as $i => $row) {
            $idx = $i + 1;

            // roomId presence & uniqueness (type/existence handled by base rules)
            if (!array_key_exists('roomId', $row)) {
                $fail("Room #{$idx} must include roomId.");
                continue;
            }
            $roomId = $row['roomId'];

            if (in_array($roomId, $seenRoomIds, true)) {
                $fail("Room #{$idx} duplicates roomId {$roomId}. Each room can appear only once in a procedure.");
                // don’t collect this interval; keep validating others
                continue;
            }
            $seenRoomIds[] = $roomId;

            // dates required by base rules, but be defensive
            if (!isset($row['starts_at'], $row['ends_at'])) {
                $fail("Room #{$idx} must include starts_at and ends_at.");
                continue;
            }

            try {
                $start = Carbon::parse($row['starts_at']);
                $end = Carbon::parse($row['ends_at']);
            } catch (\Throwable) {
                // date_format rule will handle; skip from overlap pool
                continue;
            }

            $valid = true;

            // end >= start
            if ($end->lt($start)) {
                $fail("Room #{$idx} ends_at must be on or after its starts_at.");
                $valid = false;
            }

            // inside procedure window
            if ($start->lt($procStart)) {
                $fail("Room #{$idx} starts_at must be on or after the procedure starts_at ({$procStart->format('Y-m-d H:i:s')}).");
                $valid = false;
            }
            if ($end->gt($procEnd)) {
                $fail("Room #{$idx} ends_at must be on or before the procedure ends_at ({$procEnd->format('Y-m-d H:i:s')}).");
                $valid = false;
            }

            if ($valid) {
                $intervals[] = ['index' => $i, 'start' => $start, 'end' => $end];
            }
        }

        // Global no-overlap across all submitted rooms for this procedure
        if (count($intervals) > 1) {
            usort($intervals, fn($a, $b) => $a['start'] <=> $b['start']);
            for ($j = 1; $j < count($intervals); $j++) {
                $prev = $intervals[$j - 1];
                $curr = $intervals[$j];

                // Overlap if curr.start < prev.end (touching edges is OK)
                if ($curr['start']->lt($prev['end'])) {
                    $a = $prev['index'] + 1;
                    $b = $curr['index'] + 1;
                    $fail("Room time windows #{$a} and #{$b} overlap. Rooms inside a procedure must not overlap.");
                }
            }
        }
    }
}
