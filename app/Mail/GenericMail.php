<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

/**
 * Class GenericMail
 *
 * @package App\Mail
 */
class GenericMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * @var array
     */
    public array $data;


    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(array $data)
    {
        $this->data = $data;
        $this->data['domain'] = env('DOMAIN', 'politiq.app');
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): GenericMail
    {
        return $this->subject($this->data['subject'])
            ->from('no-reply@' . env('DOMAIN', 'politiq.app'))
            ->view('emails.generic');
    }
}
