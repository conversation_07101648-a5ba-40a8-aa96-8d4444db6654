<?php

namespace App\Services;

use App\DTO\Requests\ProcedureRequestDTO;
use App\Models\Procedure;
use App\Repositories\ProcedureRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ProcedureService
{
    private ProcedureRepository $procedureRepository;

    public function __construct()
    {
        $this->procedureRepository = new ProcedureRepository();
    }

    public function getProcedures(ProcedureRequestDTO $procedureRequestDTO): LengthAwarePaginator|Collection
    {
        return $this->procedureRepository->getProcedures($procedureRequestDTO);
    }

    public function deleteProcedure(int $procedureId): void
    {
        $this->procedureRepository->deleteProcedure($procedureId);
    }

    public function upsert(array $data): Procedure
    {
        return $this->procedureRepository->upsert($data);
    }

    public function getProcedure(ProcedureRequestDTO $procedureRequestDTO): ?Procedure
    {
        return $this->procedureRepository->getProcedures($procedureRequestDTO)[0] ?? null;
    }

    public function massDelete(array $ids): void
    {
        $this->procedureRepository->massDelete($ids);
    }
}
