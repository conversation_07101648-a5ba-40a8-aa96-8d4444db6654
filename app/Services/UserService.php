<?php

namespace App\Services;

use App\DTO\Requests\UserRequestDTO;
use App\Mail\GenericMail;
use App\Models\PasswordReset;
use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class UserService
{

    private UserRepository $userRepository;

    public function __construct()
    {
        $this->userRepository = new UserRepository();
    }

    public function getUsers(UserRequestDTO $userRequestDTO): LengthAwarePaginator|Collection
    {
        return $this->userRepository->getUsers($userRequestDTO);
    }

    public function getUser(UserRequestDTO $userRequestDTO): ?User
    {
        return $this->userRepository->getUsers($userRequestDTO)[0] ?? null;
    }

    public function requestForgotPassword(User $user): string
    {
        return $this->userRepository->requestForgotPassword($user);
    }

    public function sendResetPasswordEmail(array $data): void
    {
        $timestamp = Carbon::now()->format('d F Y, H:i:s');
        $timestamp = str_replace(array_keys(trans('months.klitiki')), array_values(trans('months.klitiki')), $timestamp);

        $post['timestamp'] = $timestamp;
        $post['subject'] = trans('auth.forgotPasswordEmail.subject') . ' | ' . env('DOMAIN', 'politiq.app');
        $post['title'] = trans('auth.forgotPasswordEmail.title');
        $post['body'] = trans('auth.forgotPasswordEmail.body');

        $post['ctaText'] = trans('auth.forgotPasswordEmail.buttonCTA');
        $post['ctaLink'] = route('resetPassword', ['token' => $data['token'], 'email' => $data['email']]);

        Mail::to($data['email'])
            ->send(new GenericMail($post));
    }

    public function getPasswordReset(string $token, string $email): bool
    {
        return $this->userRepository->getPasswordReset($token, $email);
    }

    public function resetUserPassword(array $data): string
    {
        return $this->userRepository->resetUserPassword($data);
    }

    public function upsert(array $data): User
    {
        return $this->userRepository->upsert($data);
    }
}
