<?php

namespace App\Services;

use App\DTO\Requests\TagRequestDTO;
use App\Models\Tag;
use App\Repositories\TagRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class TagService
{

    private TagRepository $tagRepository;

    public function __construct()
    {
        $this->tagRepository = new TagRepository();
    }

    public function getTags(TagRequestDTO $tagRequestDTO): LengthAwarePaginator|Collection
    {
        return $this->tagRepository->getTags($tagRequestDTO);
    }

    public function deleteTag(int $tagId): void
    {
        $this->tagRepository->deleteTag($tagId);
    }

    public function upsert(array $data): Tag
    {
        return $this->tagRepository->upsert($data);
    }

    public function getTag(TagRequestDTO $tagRequestDTO): ?Tag
    {
        return $this->tagRepository->getTags($tagRequestDTO)[0] ?? null;
    }

    public function massDelete(array $ids): void
    {
        $this->tagRepository->massDelete($ids);
    }
}
