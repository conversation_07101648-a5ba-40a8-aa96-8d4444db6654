<?php

namespace App\Services;

use App\DTO\Requests\RoomRequestDTO;
use App\DTO\Requests\TagRequestDTO;
use App\Models\Room;
use App\Models\Tag;
use App\Repositories\RoomRepository;
use App\Repositories\TagRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class RoomService
{

    private RoomRepository $roomRepository;

    public function __construct()
    {
        $this->roomRepository = new RoomRepository();
    }

    public function getRooms(RoomRequestDTO $roomRequestDTO): LengthAwarePaginator|Collection
    {
        return $this->roomRepository->getRooms($roomRequestDTO);
    }

    public function getRoom(RoomRequestDTO $roomRequestDTO): ?Room
    {
        return $this->roomRepository->getRooms($roomRequestDTO)[0] ?? null;
    }

    public function upsert(array $data): Room
    {
        return $this->roomRepository->upsert($data);
    }

    public function deleteRoom(int $roomId): void
    {
        $this->roomRepository->deleteRoom($roomId);
    }

}
