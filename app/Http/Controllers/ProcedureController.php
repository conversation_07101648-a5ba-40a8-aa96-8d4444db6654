<?php

namespace App\Http\Controllers;

use App\DTO\Requests\ProcedureRequestDTO;
use App\DTO\Requests\RoomRequestDTO;
use App\Http\Requests\Procedures\AddProcedureRequest;
use App\Http\Requests\Procedures\MassDeleteProcedureRequest;
use App\Http\Requests\Procedures\UpdateProcedureRequest;
use App\Services\ProcedureService;
use App\Services\RoomService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class ProcedureController extends MainController
{
    private ProcedureService $procedureService;

    private RoomService $roomService;

    public function __construct()
    {
        parent::__construct();
        $this->procedureService = new ProcedureService();
        $this->roomService = new RoomService();
    }

    public function index(): Response
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $this->data['searchKey'] = Session::get('procedure_searchKey') ?? '';
        $this->data['activePeriod'] = Session::get('procedure_activePeriod') ?? null;

        $procedureRequestDTO = new ProcedureRequestDTO();
        $procedureRequestDTO->searchKey = $this->data['searchKey'];
        if ($this->data['activePeriod'] === 1) {
            $procedureRequestDTO->activePeriod = true;
        }
        if ($this->data['activePeriod'] === 0) {
            $procedureRequestDTO->activePeriod = false;
        }

        $procedureRequestDTO->with = ['rooms', 'rooms.room'];

        $this->data['procedures'] = $this->procedureService->getProcedures($procedureRequestDTO);

        return Inertia::render('procedure/index', $this->data);
    }

    public function indexPost(): RedirectResponse
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        Session::put('procedure_searchKey', (request()->post()['searchKey'] ?? ''));
        Session::put('activePeriod_searchKey', (request()->post()['activePeriod'] ?? null));

        return response()->redirectToRoute('procedureIndex', setLocaleParams());
    }

    public function deleteProcedure(int $id): RedirectResponse
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $this->procedureService->deleteProcedure($id);

        return redirect()->back();
    }

    public function massDeleteProcedures(MassDeleteProcedureRequest $massDeleteProcedureRequest): RedirectResponse
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $procedureIds = $massDeleteProcedureRequest->validated()['ids'];
        $this->procedureService->massDelete($procedureIds);

        return redirect()->back();
    }

    public function addProcedure(): Response
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $roomRequestDTO = new RoomRequestDTO();
        $roomRequestDTO->all = true;
        $this->data['rooms'] = $this->roomService->getRooms($roomRequestDTO);

        return Inertia::render('procedure/details', $this->data);
    }

    public function addProcedurePost(AddProcedureRequest $addProcedureRequest): RedirectResponse
    {
        $data = $addProcedureRequest->validated();
        $procedure = $this->procedureService->upsert($data);

        return response()->redirectToRoute('updateProcedure', setLocaleParams(['id' => $procedure->id]));
    }

    public function updateProcedure(): Response
    {
        $procedureRequestDTO = new ProcedureRequestDTO();
        $procedureRequestDTO->id = request()->route('id');
        $this->data['procedure'] = $this->procedureService->getProcedure($procedureRequestDTO);

        return Inertia::render('procedure/details', $this->data);
    }

    public function updateProcedurePost(UpdateProcedureRequest $updateProcedureRequest): RedirectResponse
    {
        $this->procedureService->upsert($updateProcedureRequest->validated());

        return redirect()
            ->back()
            ->with('success', 'Procedure updated successfully.');
    }
}
