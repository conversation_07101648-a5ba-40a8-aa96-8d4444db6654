<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Faker\Factory as Faker;
use Illuminate\Pagination\LengthAwarePaginator;

class TestController extends Controller
{
    public function testpage(Request $request)
    {
        $faker = Faker::create();

        $currentUser = [
            'name' => 'You.',
            'image' => 'https://i.pravatar.cc/150?u=1',
        ];

        $otherUsers = [
            [
                'name' => '<PERSON>.',
                'image' => 'https://i.pravatar.cc/150?u=5',
            ],
            [
                'name' => '<PERSON>',
                'image' => 'https://i.pravatar.cc/150?u=2',
            ],
            // add more as needed...
        ];

        $messageSamples = [
            // for "You"
            "Absolutely! Just give me a heads up if 2pm suits you.",
            "I'll review this and get back to you soon.",
            // for others
            [
                "Apologies 😔, I've got another meeting at 2pm. Could we possibly shift it to 3pm?",
                "If you have a few extra minutes, we could also go over the presentation talk format.",
            ],
            [
                "Thanks for the update!",
                "Can you send the files by tonight?",
            ],
        ];

        $comments = [];
        for ($i = 0; $i < 6; $i++) {
            $isCurrentUser = rand(0, 1);

            if ($isCurrentUser) {
                // Comment from "You"
                $comments[] = [
                    'recipient' => $currentUser,
                    'timestamp' => $faker->time('g:ia'),
                    'messages' => [
                        $faker->randomElement(array_slice($messageSamples, 0, 2)) // select from "You" examples
                    ],
                ];
            } else {
                // Comment from someone else
                $user = $faker->randomElement($otherUsers);
                $comments[] = [
                    'sender' => $user,
                    'timestamp' => $faker->time('g:ia'),
                    'messages' => $faker->randomElement(array_slice($messageSamples, 2)), // select array of messages
                ];
            }
        }

        return Inertia::render('TestPage', [
            'comments' => $comments,
        ]);
    }
}
