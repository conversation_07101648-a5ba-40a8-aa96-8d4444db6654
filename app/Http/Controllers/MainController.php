<?php

namespace App\Http\Controllers;

use App\Traits\MetaTrait;
use Illuminate\Support\Facades\App;

class MainController extends Controller
{
    use MetaTrait;

    protected array $data = [];

    public function __construct()
    {
        $this->data['language'] = App::getLocale();
        $this->data['baseUrl'] = url('');

        $this->middleware(function ($request, $next) {
            $this->setDefaultMetas();
            $this->data['csrf_token'] = csrf_token();

            if (auth()->check()) {
                $this->data['user'] = auth()->user();
                $this->data['roles'] = $this->data['user']->roles->pluck('name')
                    ->toArray();
            }

            return $next($request);
        });
    }

    protected function setDefaultMetas(): void
    {
        $this->data['meta'] = $this->setMetas(
            trans(
                'routes.'.
                request()
                    ->route()
                    ->getName().
                '.title'
            ),
            '',
            '',
            ''
        );
    }
}
