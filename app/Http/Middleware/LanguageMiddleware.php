<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class LanguageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $language = config('languages.languages')[0]['code']; // default

        foreach (config('languages.languages') as $lang) {
            if((request()->segment(1) ?? '') === $lang['code']) {
                $language = $lang['code'];
            }
        }

        App::setLocale($language);
        return $next($request);
    }
}
