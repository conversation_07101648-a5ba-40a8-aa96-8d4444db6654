<?php

namespace App\Http\Requests\Procedures;

use App\Rules\ValidProcedureRooms;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateProcedureRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id'                 => 'required|integer|exists:tags,id',
            'title'              => 'required|string|unique:tags,title',
            'description'        => 'nullable|string',
            'starts_at'          => 'date|date_format:Y-m-d H:i:s',
            'ends_at'            => 'date|date_format:Y-m-d H:i:s',
            'rooms'              => ['nullable', 'array', new ValidProcedureRooms($this->input('starts_at'), $this->input('ends_at'))],
            'room.*.roomId'      => 'integer|exists:rooms,id',
            'room.*.starts_at'   => 'required|date|date_format:Y-m-d H:i:s|after_or_equal:starts_at',
            'room.*.ends_at'     => 'required|date|date_format:Y-m-d H:i:s|before_or_equal:ends_at|after_or_equal:rooms.*.starts_at',
            'room.*.description' => 'nullable|string',
        ];
    }

    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'errors'  => $validator->errors(),
        ], 422));
    }
}
