<?php

namespace App\Http\Requests\Room;

use Illuminate\Foundation\Http\FormRequest;

class AddRoomRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title'       => 'required|string',
            'description' => 'required|string',
            'adminId'     => 'required|numeric|exists:users,id',
            'tagIds'      => 'required|array',
            'tagIds.*'    => 'required|numeric|exists:tags,id',
            'userIds'     => 'required|array',
            'userIds.*'   => 'required|numeric|exists:users,id',
        ];
    }
}
