<?php

namespace App\Models;

use App\Scopes\DeletedScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Room extends Model
{
    /** @use HasFactory<\Database\Factories\RoomFactory> */
    use HasFactory;

    protected $fillable = ['title', 'description', 'adminId', 'isDeleted'];

    protected static function booted(): void
    {
        static::addGlobalScope(new DeletedScope());
    }

    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'adminId');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'room_tags', 'roomId', 'tagId')->withTimestamps();
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'room_users', 'roomId', 'userId')->withTimestamps();
    }

    public function procedures(): BelongsToMany
    {
        return $this->belongsToMany(Procedure::class, 'procedure_rooms', 'roomId', 'procedureId')
            ->withPivot(['starts_at', 'ends_at', 'description'])
            ->withTimestamps();
    }
}
