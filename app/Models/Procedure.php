<?php

namespace App\Models;

use App\Scopes\DeletedScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 */
class Procedure extends Model
{
    /** @use HasFactory<\Database\Factories\ProcedureFactory> */
    use HasFactory;

    protected $fillable = ['title', 'description', 'starts_at', 'ends_at', 'isDeleted'];

    protected static function booted(): void
    {
        static::addGlobalScope(new DeletedScope());
    }

    // This relation is used to show the rooms in the list (Read endpoints)
    public function rooms(): HasMany
    {
        return $this->hasMany(ProcedureRoom::class, 'procedureId');
    }

    // This relation is used to sync the rooms (write endpoints)
    public function procedureRooms(): BelongsToMany
    {
        return $this->belongsToMany(\App\Models\Room::class, 'procedure_rooms', 'procedureId', 'roomId')
            ->withPivot(['starts_at', 'ends_at'])
            ->withTimestamps();
    }
}
