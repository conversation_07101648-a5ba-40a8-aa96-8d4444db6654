<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Tag extends Model
{
    /** @use HasFactory<\Database\Factories\TagFactory> */
    use HasFactory;
    protected $fillable = ['title'];

    protected static function booted(): void
    {
        static::deleting(function ($tag) {
            if ($tag->system) {
                throw new \Exception("System tags cannot be deleted.");
            }
        });

        static::updating(function ($tag) {
            if ($tag->system) {
                throw new \Exception("System tags cannot be updated.");
            }
        });
    }

    public function rooms(): BelongsToMany
    {
        return $this->belongsToMany(Room::class, 'room_tags', 'tagId', 'roomId');
    }
}
