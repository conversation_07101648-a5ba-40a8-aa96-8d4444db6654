<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PasswordReset extends Model
{
    protected $table = 'password_reset_tokens';
    protected $fillable = ['email', 'token'];

    protected static function booted()
    {
        static::creating(function ($passwordReset) {
            // Temporarily set a token so the record can be saved
            $passwordReset->token = Str::random(40);
        });

        static::created(function ($passwordReset) {
            // Update token now that ID exists
            $passwordReset->token = $passwordReset->id . $passwordReset->token;
            $passwordReset->save();
        });
    }

}
