<?php

namespace App\Traits;

use App\DTO\Responses\MetaDTO;

/**
 * A trait to set metas
 *
 * Trait MetaTrait
 *
 * @package App\Http\Traits
 */
trait MetaTrait {
    /**
     * @param string $title
     * @param string $description
     * @param string $keys
     * @param string $image
     * @return MetaDTO
     */
    public function setMetas(string $title = '', string $description = '', string $keys = '', string $image = ''): MetaDTO
    {
        $meta = new MetaDTO();
        $meta->title = $title;
        $meta->description = $description ?: 'TODO - set default';
        $meta->keys = $keys ?:  'TODO - set default';
        $meta->image = $image ?: 'TODO - set default';
        return $meta;
    }
}
