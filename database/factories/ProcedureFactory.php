<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Procedure>
 */
class ProcedureFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Start anywhere from last month to next month
        $startsAt = $this->faker->dateTimeBetween('-1 month', '+1 month');

        // Duration between 30 minutes and 8 hours
        $durationDays = $this->faker->numberBetween(1, 30);
        $endsAt = (clone $startsAt)->modify("+{$durationDays} days");

        return [
            'title'       => $this->faker->unique()
                ->sentence(3),          // e.g. "Review safety checklist"
            'description' => $this->faker->optional()
                ->paragraphs(2, true), // mediumText ok
            'starts_at'   => $startsAt,
            'ends_at'     => $endsAt,
            'isDeleted'   => false,
        ];
    }
}
