<?php

namespace Database\Seeders;

use App\Models\Tag;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // We need to make it like this and not using factory, as system is not fillable for security reasons

        foreach(config('tags.systemTags') as $tagTitle) {
            DB::table('tags')
                ->insert([
                    ['title' => $tagTitle, 'system' => true],
                ]);
        }

        Tag::factory(15)->create();
    }
}
