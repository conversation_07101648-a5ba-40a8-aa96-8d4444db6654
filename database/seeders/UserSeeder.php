<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = config('users.roles');

        foreach ($roles as $role) {
            Role::create(['name' => $role]);
        }

        $administrators[] = User::factory()
            ->create([
            'name' => 'sf',
            'surname' => 'sf',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'isActive' => 1
        ]);
        $administrators[] = User::factory()
            ->create([
            'name' => 'ab',
            'surname' => 'ab',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'isActive' => 1
        ]);
        $administrators[] = User::factory()
            ->create([
            'name' => 'pg',
            'surname' => 'pg',
            'email' => 'pgkat<PERSON>id<PERSON>@gmail.com',
            'password' => Hash::make('password'),
            'isActive' => 1
        ]);

        foreach ($administrators as $administrator) {
            $administrator->assignRole('administrator');
        }
    }
}
