<?php

namespace Database\Seeders;

use App\Models\Procedure;
use App\Models\Room;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class ProcedureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $procedures = Procedure::factory(15)
            ->create();

        $roomIds = Room::query()
            ->pluck('id')
            ->all();
        foreach ($procedures as $procedure) {
            $k = random_int(0, 3); // 0..3 rooms
            if ($k === 0) {
                continue;
            }

            $selectedRoomIds = collect($roomIds)
                ->shuffle()
                ->take($k)
                ->values();
            $procedureStart = Carbon::parse($procedure->starts_at);
            $procedureEnd = Carbon::parse($procedure->ends_at);
            $totalSeconds = $procedureStart->diffInSeconds($procedureEnd, false);

            $i = 1;
            foreach ($selectedRoomIds as $roomId) {
                $startDate = Carbon::parse($procedure->starts_at)
                    ->addDays($i * 2);
                $procedure->rooms()
                    ->attach($roomId, [
                        'starts_at' => $startDate,
                        'ends_at'   => Carbon::parse($startDate)
                            ->addDay(),
                    ]);

                $i++;
            }
        }
    }
}
