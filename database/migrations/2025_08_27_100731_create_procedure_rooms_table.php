<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('procedure_rooms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('procedureId')
                ->constrained('procedures')
                ->cascadeOnDelete();
            $table->foreignId('roomId')
                ->constrained('rooms')
                ->cascadeOnDelete();
            $table->dateTime('starts_at');
            $table->dateTime('ends_at');
            $table->mediumText('description')
                ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('procedure_rooms');
    }
};
