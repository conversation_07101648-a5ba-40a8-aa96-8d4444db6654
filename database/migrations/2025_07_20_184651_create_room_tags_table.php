<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('room_tags', function (Blueprint $table) {
            $table->id();
            $table->foreignId('roomId')->constrained('rooms')->cascadeOnDelete();
            $table->foreignId('tagId')->constrained('tags')->cascadeOnDelete();
            $table->timestamps();

            $table->unique(['roomId', 'tagId']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('room_tags');
    }
};
